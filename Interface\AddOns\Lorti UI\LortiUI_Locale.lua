-- LortiUI Localization
LortiUI_Locale = {}
local L = LortiUI_Locale

-- English (default)
L["Lorti UI"] = "Lorti UI"
L["Frame Colors"] = "Frame Colors"
L["Action Bars"] = "Action Bars"
L["Media Effects"] = "Media Effects"
L["Minimap"] = "Minimap"
L["Customize the colors of unit frames (player, target, focus, etc.)"] = "Customize the colors of unit frames (player, target, focus, etc.)"
L["Player Frame"] = "Player Frame"
L["Enable Player Color"] = "Enable Player Color"
L["Enable color for player frame"] = "Enable color for player frame"
L["Player Frame Color"] = "Player Frame Color"
L["Sets the color for player frame texture"] = "Sets the color for player frame texture"
L["Target Frame"] = "Target Frame"
L["Enable Target Color"] = "Enable Target Color"
L["Enable color for target frame"] = "Enable color for target frame"
L["Target Frame Color"] = "Target Frame Color"
L["Sets the color for target frame texture"] = "Sets the color for target frame texture"
L["Focus Frame"] = "Focus Frame"
L["Enable Focus Color"] = "Enable Focus Color"
L["Enable color for focus frame"] = "Enable color for focus frame"
L["Focus Frame Color"] = "Focus Frame Color"
L["Sets the color for focus frame texture"] = "Sets the color for focus frame texture"
L["Party Frames"] = "Party Frames"
L["Enable Party Color"] = "Enable Party Color"
L["Enable color for party frames"] = "Enable color for party frames"
L["Party Frame Color"] = "Party Frame Color"
L["Sets the color for party member frame textures"] = "Sets the color for party member frame textures"
L["Pet Frame"] = "Pet Frame"
L["Enable Pet Color"] = "Enable Pet Color"
L["Enable color for pet frame"] = "Enable color for pet frame"
L["Pet Frame Color"] = "Pet Frame Color"
L["Sets the color for pet frame texture"] = "Sets the color for pet frame texture"
L["Vehicle Frame"] = "Vehicle Frame"
L["Enable Vehicle Color"] = "Enable Vehicle Color"
L["Enable color for vehicle frame"] = "Enable color for vehicle frame"
L["Vehicle Frame Color"] = "Vehicle Frame Color"
L["Sets the color for vehicle frame texture"] = "Sets the color for vehicle frame texture"
L["Boss Frames"] = "Boss Frames"
L["Enable Boss Color"] = "Enable Boss Color"
L["Enable color for boss frames"] = "Enable color for boss frames"
L["Boss Frame Color"] = "Boss Frame Color"
L["Sets the color for boss frame textures"] = "Sets the color for boss frame textures"
L["Arena Frames"] = "Arena Frames"
L["Enable Arena Color"] = "Enable Arena Color"
L["Enable color for arena frames"] = "Enable color for arena frames"
L["Arena Frame Color"] = "Arena Frame Color"
L["Sets the color for arena frame textures"] = "Sets the color for arena frame textures"
L["Customize the appearance of action bar buttons"] = "Customize the appearance of action bar buttons"
L["Main Settings"] = "Main Settings"
L["Enable Button Styling"] = "Enable Button Styling"
L["Enable styling for action bar buttons"] = "Enable styling for action bar buttons"
L["Styling Options"] = "Styling Options"
L["Enable Button Background"] = "Enable Button Background"
L["Show background texture on buttons"] = "Show background texture on buttons"
L["Enable Button Shadow"] = "Enable Button Shadow"
L["Show shadow around buttons"] = "Show shadow around buttons"
L["Use Flat Background"] = "Use Flat Background"
L["Use flat color instead of textured background"] = "Use flat color instead of textured background"
L["Addon Compatibility"] = "Addon Compatibility"
L["Style Standard Bars"] = "Style Standard Bars"
L["Style default WoW action bars"] = "Style default WoW action bars"
L["Style Dominos Bars"] = "Style Dominos Bars"
L["Style Dominos addon bars (if installed)"] = "Style Dominos addon bars (if installed)"
L["Style Bartender Bars"] = "Style Bartender Bars"
L["Style Bartender addon bars (if installed)"] = "Style Bartender addon bars (if installed)"
L["Style Other Addon Bars"] = "Style Other Addon Bars"
L["Style bars from other addons"] = "Style bars from other addons"
L["Advanced Options"] = "Advanced Options"
L["Force Button Styling"] = "Force Button Styling"
L["Force styling even with conflicting addons (may cause issues)"] = "Force styling even with conflicting addons (may cause issues)"
L["Enable Universal Styling"] = "Enable Universal Styling"
L["Style ALL buttons, not just action bars (experimental, may cause lag)"] = "Style ALL buttons, not just action bars (experimental, may cause lag)"
L["Color Options"] = "Color Options"
L["Button Background Color"] = "Button Background Color"
L["Color for button backgrounds"] = "Color for button backgrounds"
L["Button Shadow Color"] = "Button Shadow Color"
L["Color for button shadows"] = "Color for button shadows"
L["Button Normal Color"] = "Button Normal Color"
L["Color for normal button state"] = "Color for normal button state"
L["Add visual effects and enhancements to your interface"] = "Add visual effects and enhancements to your interface"
L["Glow Effects"] = "Glow Effects"
L["Enable Glow Effects"] = "Enable Glow Effects"
L["Enable glow effects on frames"] = "Enable glow effects on frames"
L["Enable Player Glow"] = "Enable Player Glow"
L["Enable glow effect on player frame"] = "Enable glow effect on player frame"
L["Enable Target Glow"] = "Enable Target Glow"
L["Enable glow effect on target frame"] = "Enable glow effect on target frame"
L["Glow Settings"] = "Glow Settings"
L["Glow Intensity"] = "Glow Intensity"
L["Intensity of the glow effect"] = "Intensity of the glow effect"
L["Glow Size"] = "Glow Size"
L["Size of the glow effect"] = "Size of the glow effect"
L["Transparency Effects"] = "Transparency Effects"
L["Enable Transparency Effects"] = "Enable Transparency Effects"
L["Enable transparency effects on frames"] = "Enable transparency effects on frames"
L["Frame Transparency"] = "Frame Transparency"
L["Transparency level for frames"] = "Transparency level for frames"
L["Animation Effects"] = "Animation Effects"
L["Enable Animations"] = "Enable Animations"
L["Enable animation effects"] = "Enable animation effects"
L["Animation Speed"] = "Animation Speed"
L["Speed of animations"] = "Speed of animations"
L["Customize minimap appearance and behavior"] = "Customize minimap appearance and behavior"
L["Minimap Enhancements"] = "Minimap Enhancements"
L["Enable Minimap Enhancements"] = "Enable Minimap Enhancements"
L["Enable minimap visual enhancements"] = "Enable minimap visual enhancements"
L["Minimap Scale"] = "Minimap Scale"
L["Scale of the minimap"] = "Scale of the minimap"
L["Enable Minimap Border"] = "Enable Minimap Border"
L["Show border around minimap"] = "Show border around minimap"
L["Enable Minimap Background"] = "Enable Minimap Background"
L["Show background behind minimap"] = "Show background behind minimap"
L["Minimap Transparency"] = "Minimap Transparency"
L["Transparency level for minimap"] = "Transparency level for minimap"
L["Enable Minimap Move"] = "Enable Minimap Move"
L["Allow moving the minimap"] = "Allow moving the minimap"
L["Enable Error Message Filter"] = "Enable Error Message Filter"
L["Hide annoying error messages like 'Ability is not ready yet'"] = "Hide annoying error messages like 'Ability is not ready yet'"

-- Section headers (not translated in English)
L["Size and Scale"] = "Size and Scale"
L["Border and Background"] = "Border and Background"
L["Transparency"] = "Transparency"
L["Position"] = "Position"
L["Current Status"] = "Current Status"
L["Status"] = "Status"
L["Enabled"] = "Enabled"
L["Disabled"] = "Disabled"
L["Active"] = "Active"
L["Inactive"] = "Inactive"
L["Working"] = "Working"
L["Not Working"] = "Not Working"
L["Lorti UI loaded. Use /lortiui or /lui to open settings."] = "Lorti UI loaded. Use /lortiui or /lui to open settings."
L["Colors applied to all frames"] = "Colors applied to all frames"
L["All colors reset to default black"] = "All colors reset to default black"
L["Usage: /lortiui - open settings, /lortiui apply - apply colors, /lortiui reset - reset to default"] = "Usage: /lortiui - open settings, /lortiui apply - apply colors, /lortiui reset - reset to default"
L["Action bar styling disabled due to conflicting addons (Masque, Dominos, Bartender4). Enable 'Force Button Styling' to override."] = "Action bar styling disabled due to conflicting addons (Masque, Dominos, Bartender4). Enable 'Force Button Styling' to override."
L["Colors applied!"] = "Colors applied!"
L["Colors reset to default!"] = "Colors reset to default!"
L["Action bars styled!"] = "Action bars styled!"

-- Russian
if GetLocale() == "ruRU" then
    L["Lorti UI"] = "Lorti UI"
    L["Frame Colors"] = "Цвета Фреймов"
    L["Action Bars"] = "Панели Действий"
    L["Media Effects"] = "Медиа Эффекты"
    L["Minimap"] = "Миникарта"
    L["Customize the colors of unit frames (player, target, focus, etc.)"] = "Настройка цветов фреймов юнитов (игрок, цель, фокус и т.д.)"
    L["Player Frame"] = "Фрейм Игрока"
    L["Enable Player Color"] = "Включить Цвет Игрока"
    L["Enable color for player frame"] = "Включить цвет для фрейма игрока"
    L["Player Frame Color"] = "Цвет Фрейма Игрока"
    L["Sets the color for player frame texture"] = "Устанавливает цвет для текстуры фрейма игрока"
    L["Target Frame"] = "Фрейм Цели"
    L["Enable Target Color"] = "Включить Цвет Цели"
    L["Enable color for target frame"] = "Включить цвет для фрейма цели"
    L["Target Frame Color"] = "Цвет Фрейма Цели"
    L["Sets the color for target frame texture"] = "Устанавливает цвет для текстуры фрейма цели"
    L["Focus Frame"] = "Фрейм Фокуса"
    L["Enable Focus Color"] = "Включить Цвет Фокуса"
    L["Enable color for focus frame"] = "Включить цвет для фрейма фокуса"
    L["Focus Frame Color"] = "Цвет Фрейма Фокуса"
    L["Sets the color for focus frame texture"] = "Устанавливает цвет для текстуры фрейма фокуса"
    L["Party Frames"] = "Фреймы Группы"
    L["Enable Party Color"] = "Включить Цвет Группы"
    L["Enable color for party frames"] = "Включить цвет для фреймов группы"
    L["Party Frame Color"] = "Цвет Фреймов Группы"
    L["Sets the color for party member frame textures"] = "Устанавливает цвет для текстур фреймов участников группы"
    L["Pet Frame"] = "Фрейм Питомца"
    L["Enable Pet Color"] = "Включить Цвет Питомца"
    L["Enable color for pet frame"] = "Включить цвет для фрейма питомца"
    L["Pet Frame Color"] = "Цвет Фрейма Питомца"
    L["Sets the color for pet frame texture"] = "Устанавливает цвет для текстуры фрейма питомца"
    L["Vehicle Frame"] = "Фрейм Транспорта"
    L["Enable Vehicle Color"] = "Включить Цвет Транспорта"
    L["Enable color for vehicle frame"] = "Включить цвет для фрейма транспорта"
    L["Vehicle Frame Color"] = "Цвет Фрейма Транспорта"
    L["Sets the color for vehicle frame texture"] = "Устанавливает цвет для текстуры фрейма транспорта"
    L["Boss Frames"] = "Фреймы Боссов"
    L["Enable Boss Color"] = "Включить Цвет Боссов"
    L["Enable color for boss frames"] = "Включить цвет для фреймов боссов"
    L["Boss Frame Color"] = "Цвет Фреймов Боссов"
    L["Sets the color for boss frame textures"] = "Устанавливает цвет для текстур фреймов боссов"
    L["Arena Frames"] = "Фреймы Арены"
    L["Enable Arena Color"] = "Включить Цвет Арены"
    L["Enable color for arena frames"] = "Включить цвет для фреймов арены"
    L["Arena Frame Color"] = "Цвет Фреймов Арены"
    L["Sets the color for arena frame textures"] = "Устанавливает цвет для текстур фреймов арены"

    -- Action Bars tab
    L["Customize the appearance of action bar buttons"] = "Настройка внешнего вида кнопок панелей действий"
    L["Main Settings"] = "Основные Настройки"
    L["Enable Button Styling"] = "Включить Стилизацию Кнопок"
    L["Enable styling for action bar buttons"] = "Включить стилизацию кнопок панелей действий"
    L["Styling Options"] = "Опции Стилизации"
    L["Enable Button Background"] = "Включить Фон Кнопок"
    L["Show background texture on buttons"] = "Показывать фоновую текстуру на кнопках"
    L["Enable Button Shadow"] = "Включить Тень Кнопок"
    L["Show shadow around buttons"] = "Показывать тень вокруг кнопок"
    L["Use Flat Background"] = "Использовать Плоский Фон"
    L["Use flat color instead of textured background"] = "Использовать плоский цвет вместо текстурированного фона"
    L["Addon Compatibility"] = "Совместимость с Аддонами"
    L["Style Standard Bars"] = "Стилизовать Стандартные Панели"
    L["Style default WoW action bars"] = "Стилизовать стандартные панели действий WoW"
    L["Style Dominos Bars"] = "Стилизовать Панели Dominos"
    L["Style Dominos addon bars (if installed)"] = "Стилизовать панели аддона Dominos (если установлен)"
    L["Style Bartender Bars"] = "Стилизовать Панели Bartender"
    L["Style Bartender addon bars (if installed)"] = "Стилизовать панели аддона Bartender (если установлен)"
    L["Style Other Addon Bars"] = "Стилизовать Панели Других Аддонов"
    L["Style bars from other addons"] = "Стилизовать панели других аддонов"
    L["Advanced Options"] = "Расширенные Опции"
    L["Force Button Styling"] = "Принудительная Стилизация Кнопок"
    L["Force styling even with conflicting addons (may cause issues)"] = "Принудительная стилизация даже с конфликтующими аддонами (может вызвать проблемы)"
    L["Enable Universal Styling"] = "Включить Универсальную Стилизацию"
    L["Style ALL buttons, not just action bars (experimental, may cause lag)"] = "Стилизовать ВСЕ кнопки, не только панели действий (экспериментально, может вызвать лаги)"
    L["Color Options"] = "Опции Цветов"
    L["Button Background Color"] = "Цвет Фона Кнопок"
    L["Color for button backgrounds"] = "Цвет для фона кнопок"
    L["Button Shadow Color"] = "Цвет Тени Кнопок"
    L["Color for button shadows"] = "Цвет для теней кнопок"
    L["Button Normal Color"] = "Обычный Цвет Кнопок"
    L["Color for normal button state"] = "Цвет для обычного состояния кнопок"

    -- Media Effects tab
    L["Add visual effects and enhancements to your interface"] = "Добавить визуальные эффекты и улучшения к вашему интерфейсу"
    L["Glow Effects"] = "Эффекты Свечения"
    L["Enable Glow Effects"] = "Включить Эффекты Свечения"
    L["Enable glow effects on frames"] = "Включить эффекты свечения на фреймах"
    L["Enable Player Glow"] = "Включить Свечение Игрока"
    L["Enable glow effect on player frame"] = "Включить эффект свечения на фрейме игрока"
    L["Enable Target Glow"] = "Включить Свечение Цели"
    L["Enable glow effect on target frame"] = "Включить эффект свечения на фрейме цели"
    L["Glow Settings"] = "Настройки Свечения"
    L["Glow Intensity"] = "Интенсивность Свечения"
    L["Intensity of the glow effect"] = "Интенсивность эффекта свечения"
    L["Glow Size"] = "Размер Свечения"
    L["Size of the glow effect"] = "Размер эффекта свечения"
    L["Transparency Effects"] = "Эффекты Прозрачности"
    L["Enable Transparency Effects"] = "Включить Эффекты Прозрачности"
    L["Enable transparency effects on frames"] = "Включить эффекты прозрачности на фреймах"
    L["Frame Transparency"] = "Прозрачность Фреймов"
    L["Transparency level for frames"] = "Уровень прозрачности для фреймов"
    L["Animation Effects"] = "Эффекты Анимации"
    L["Enable Animations"] = "Включить Анимации"
    L["Enable animation effects"] = "Включить эффекты анимации"
    L["Animation Speed"] = "Скорость Анимации"
    L["Speed of animations"] = "Скорость анимаций"

    -- Minimap tab
    L["Customize minimap appearance and behavior"] = "Настройка внешнего вида и поведения миникарты"
    L["Minimap Enhancements"] = "Улучшения Миникарты"
    L["Enable Minimap Enhancements"] = "Включить Улучшения Миникарты"
    L["Enable minimap visual enhancements"] = "Включить визуальные улучшения миникарты"
    L["Minimap Scale"] = "Масштаб Миникарты"
    L["Scale of the minimap"] = "Масштаб миникарты"
    L["Enable Minimap Border"] = "Включить Границу Миникарты"
    L["Show border around minimap"] = "Показывать границу вокруг миникарты"
    L["Enable Minimap Background"] = "Включить Фон Миникарты"
    L["Show background behind minimap"] = "Показывать фон за миникартой"
    L["Minimap Transparency"] = "Прозрачность Миникарты"
    L["Transparency level for minimap"] = "Уровень прозрачности для миникарты"
    L["Enable Minimap Move"] = "Включить Перемещение Миникарты"
    L["Allow moving the minimap"] = "Разрешить перемещение миникарты"
    L["Enable Error Message Filter"] = "Включить Фильтр Сообщений об Ошибках"
    L["Hide annoying error messages like 'Ability is not ready yet'"] = "Скрывать раздражающие сообщения об ошибках типа 'Способность еще не готова'"

    -- Section headers
    L["Size and Scale"] = "Размер и Масштаб"
    L["Border and Background"] = "Граница и Фон"
    L["Transparency"] = "Прозрачность"
    L["Position"] = "Позиция"

    -- Status messages
    L["Current Status"] = "Текущий Статус"
    L["Status"] = "Статус"
    L["Enabled"] = "Включено"
    L["Disabled"] = "Отключено"
    L["Active"] = "Активно"
    L["Inactive"] = "Неактивно"
    L["Working"] = "Работает"
    L["Not Working"] = "Не Работает"

    -- Console messages
    L["Lorti UI loaded. Use /lortiui or /lui to open settings."] = "Lorti UI загружен. Используйте /lortiui или /lui для открытия настроек."
    L["Colors applied to all frames"] = "Цвета применены ко всем фреймам"
    L["All colors reset to default black"] = "Все цвета сброшены к черному по умолчанию"
    L["Usage: /lortiui - open settings, /lortiui apply - apply colors, /lortiui reset - reset to default"] = "Использование: /lortiui - открыть настройки, /lortiui apply - применить цвета, /lortiui reset - сбросить к умолчанию"
    L["Action bar styling disabled due to conflicting addons (Masque, Dominos, Bartender4). Enable 'Force Button Styling' to override."] = "Стилизация панелей действий отключена из-за конфликтующих аддонов (Masque, Dominos, Bartender4). Включите 'Принудительную Стилизацию Кнопок' для переопределения."
    L["Colors applied!"] = "Цвета применены!"
    L["Colors reset to default!"] = "Цвета сброшены к умолчанию!"
    L["Action bars styled!"] = "Панели действий стилизованы!"
end
