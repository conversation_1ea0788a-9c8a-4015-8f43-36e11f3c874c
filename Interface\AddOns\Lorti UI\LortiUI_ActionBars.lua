-- LortiUI Action Bars Module
-- MINIMAL VERSION - Only backgrounds, no button styling

local LortiUI = LibStub("AceAddon-3.0"):GetAddon("LortiUI")

-- Default configuration values
local defaults = {
    enableButtonBackground = false,
    useFlatButtonBackground = false,
    buttonBackgroundColor = {r = 0, g = 0, b = 0, a = 0.5},
    enableButtonShadow = false,
    buttonShadowColor = {r = 0, g = 0, b = 0, a = 1},
    enableButtonStyling = true,
}

-- Get texture path with fallback
local function getTexture(textureName)
    local texturePath = "Interface\\AddOns\\Lorti UI\\Textures\\" .. textureName
    return texturePath
end

-- Create backdrop structure
local function createBackdrop()
    return {
        bgFile = "Interface\\Tooltips\\UI-Tooltip-Background",
        edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
        tile = true,
        tileSize = 16,
        edgeSize = 16,
        insets = {left = 4, right = 4, top = 4, bottom = 4}
    }
end

-- Apply background to button
local function applyBackground(button)
    if not button or button.lortiBackground then return end
    
    local db = LortiUI.db.profile
    if not (db.enableButtonBackground or db.enableButtonShadow) then return end
    
    -- Create background frame
    local bg = CreateFrame("Frame", nil, button)
    bg:SetPoint("TOPLEFT", button, "TOPLEFT", -4, 4)
    bg:SetPoint("BOTTOMRIGHT", button, "BOTTOMRIGHT", 4, -4)
    bg:SetFrameLevel(math.max(button:GetFrameLevel() - 1, 0))
    
    -- Apply backdrop
    bg:SetBackdrop(createBackdrop())
    
    if db.enableButtonBackground then
        if not db.useFlatButtonBackground then
            -- Textured background with fallback
            local texture = bg:CreateTexture(nil, "BACKGROUND", nil, -8)
            texture:SetTexture(getTexture("buttonBackground"))
            texture:SetAllPoints(button)
            local color = db.buttonBackgroundColor
            texture:SetVertexColor(color.r, color.g, color.b, color.a)
        else
            -- Flat color background
            local color = db.buttonBackgroundColor
            bg:SetBackdropColor(color.r, color.g, color.b, color.a)
        end
    end
    
    if db.enableButtonShadow then
        local color = db.buttonShadowColor
        bg:SetBackdropBorderColor(color.r, color.g, color.b, color.a)
    end
    
    button.lortiBackground = bg
end

-- Remove background only
local function removeBackground(button)
    if not button then return end
    
    if button.lortiBackground then
        button.lortiBackground:Hide()
        button.lortiBackground = nil
    end
end

-- Apply button textures only
local function applyButtonTextures(button)
    if not button then return end

    local db = LortiUI.db.profile
    if not db.enableButtonStyling then return end

    -- Apply custom textures from media folder
    if button.SetNormalTexture then
        button:SetNormalTexture("Interface\\AddOns\\Lorti UI\\media\\button_background")
    end
    if button.SetHighlightTexture then
        button:SetHighlightTexture("Interface\\AddOns\\Lorti UI\\media\\hover")
    end
    if button.SetPushedTexture then
        button:SetPushedTexture("Interface\\AddOns\\Lorti UI\\media\\pushed")
    end
end

-- Apply icon positioning
local function applyIconPositioning(button)
    if not button then return end

    local db = LortiUI.db.profile
    if not db.enableButtonStyling then return end

    local name = button:GetName()
    if not name then return end

    -- Position icon with small padding
    local icon = _G[name.."Icon"]
    if icon then
        icon:ClearAllPoints()
        icon:SetPoint("TOPLEFT", button, "TOPLEFT", 2, -2)
        icon:SetPoint("BOTTOMRIGHT", button, "BOTTOMRIGHT", -2, 2)
    end

    -- Position cooldown
    local cooldown = _G[name.."Cooldown"]
    if cooldown then
        cooldown:ClearAllPoints()
        cooldown:SetPoint("TOPLEFT", button, "TOPLEFT", 2, -2)
        cooldown:SetPoint("BOTTOMRIGHT", button, "BOTTOMRIGHT", -2, 2)
    end
end

-- Style a single button
local function styleButton(button)
    if not button or button.lortiStyled then return end

    local db = LortiUI.db.profile

    -- Apply textures first
    applyButtonTextures(button)

    -- Apply icon positioning
    applyIconPositioning(button)

    -- Apply backgrounds
    if db.enableButtonBackground or db.enableButtonShadow then
        applyBackground(button)
    else
        removeBackground(button)
    end

    button.lortiStyled = true
end

-- Restore original button textures
local function restoreButtonTextures(button)
    if not button then return end

    -- Restore original WoW textures
    if button.SetNormalTexture then
        button:SetNormalTexture("Interface\\Buttons\\UI-Quickslot2")
    end
    if button.SetHighlightTexture then
        button:SetHighlightTexture("Interface\\Buttons\\ButtonHilight-Square")
    end
    if button.SetPushedTexture then
        button:SetPushedTexture("Interface\\Buttons\\UI-Quickslot-Depress")
    end
end

-- Restore original icon positioning
local function restoreIconPositioning(button)
    if not button then return end

    local name = button:GetName()
    if not name then return end

    -- Restore icon to default position
    local icon = _G[name.."Icon"]
    if icon then
        icon:ClearAllPoints()
        icon:SetAllPoints(button)
    end

    -- Restore cooldown to default position
    local cooldown = _G[name.."Cooldown"]
    if cooldown then
        cooldown:ClearAllPoints()
        cooldown:SetAllPoints(button)
    end
end

-- Remove styling from a button
local function unstyleButton(button)
    if not button then return end

    restoreButtonTextures(button)
    restoreIconPositioning(button)
    removeBackground(button)
    button.lortiStyled = nil
end

-- Get all action buttons
local function getAllActionButtons()
    local buttons = {}
    
    -- Standard action buttons
    for i = 1, 12 do
        local button = _G["ActionButton" .. i]
        if button then table.insert(buttons, button) end
    end
    
    -- MultiBar buttons
    for i = 1, 12 do
        local button = _G["MultiBarBottomLeftButton" .. i]
        if button then table.insert(buttons, button) end
        
        button = _G["MultiBarBottomRightButton" .. i]
        if button then table.insert(buttons, button) end
        
        button = _G["MultiBarLeftButton" .. i]
        if button then table.insert(buttons, button) end
        
        button = _G["MultiBarRightButton" .. i]
        if button then table.insert(buttons, button) end
    end
    
    return buttons
end

-- Apply styling to all buttons
local function applyToAllButtons()
    local buttons = getAllActionButtons()
    for _, button in ipairs(buttons) do
        -- Force remove old styling first
        button.lortiStyled = nil
        styleButton(button)
    end
end

-- Remove styling from all buttons
local function removeFromAllButtons()
    local buttons = getAllActionButtons()
    for _, button in ipairs(buttons) do
        unstyleButton(button)
    end
end

-- Update all buttons based on settings
function LortiUI:UpdateActionBarStyling()
    local db = self.db.profile

    if db.enableButtonStyling or db.enableButtonBackground or db.enableButtonShadow then
        applyToAllButtons()
    else
        removeFromAllButtons()
    end
end

-- Initialize action bar styling
function LortiUI:InitializeActionBars()
    -- Set defaults
    for key, value in pairs(defaults) do
        if self.db.profile[key] == nil then
            self.db.profile[key] = value
        end
    end
    
    -- Apply initial styling
    self:UpdateActionBarStyling()
end

-- Event handler
local function OnEvent(self, event, ...)
    if event == "PLAYER_LOGIN" then
        LortiUI:InitializeActionBars()
    end
end

-- Register events
local frame = CreateFrame("Frame")
frame:RegisterEvent("PLAYER_LOGIN")
frame:SetScript("OnEvent", OnEvent)
