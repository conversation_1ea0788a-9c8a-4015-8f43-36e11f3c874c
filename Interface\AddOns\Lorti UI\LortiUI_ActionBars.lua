-- Lorti UI Action Bars Module
-- Enhanced action button styling with media textures

local LortiUI = LibStub("AceAddon-3.0"):GetAddon("LortiUI")

-- Get localization table
local function getL()
    return LortiUI_Locale or {}
end

-- Media paths with fallbacks to WoW default textures
local MEDIA_PATH = "Interface\\AddOns\\Lorti UI\\media\\"
local MEDIA_TEXTURES = {
    buttonBackground = MEDIA_PATH .. "button_background",
    buttonBackgroundFlat = MEDIA_PATH .. "button_background_flat",
    checked = MEDIA_PATH .. "checked",
    flash = MEDIA_PATH .. "flash",
    gloss = MEDIA_PATH .. "gloss",
    glossGrey = MEDIA_PATH .. "gloss_grey",
    hover = MEDIA_PATH .. "hover",
    outerShadow = MEDIA_PATH .. "outer_shadow",
    pushed = MEDIA_PATH .. "pushed",
}

-- Fallback textures when media files are missing
local FALLBACK_TEXTURES = {
    buttonBackground = "Interface\\Buttons\\UI-ActionButton-Border",
    buttonBackgroundFlat = "Interface\\Buttons\\UI-ActionButton-Border",
    checked = "Interface\\Buttons\\CheckButtonHilight",
    flash = "Interface\\Buttons\\UI-QuickslotRed",
    gloss = "Interface\\Buttons\\UI-ActionButton-Border",
    glossGrey = "Interface\\Buttons\\UI-ActionButton-Border",
    hover = "Interface\\Buttons\\UI-Common-MouseHilight",
    outerShadow = "Interface\\Tooltips\\UI-Tooltip-Background",
    pushed = "Interface\\Buttons\\UI-Quickslot-Depress",
}

-- Function to get texture with fallback
local function getTexture(textureKey)
    local customTexture = MEDIA_TEXTURES[textureKey]
    local fallbackTexture = FALLBACK_TEXTURES[textureKey]

    -- Try to use custom texture, fallback to WoW default if missing
    return customTexture or fallbackTexture or "Interface\\Buttons\\WHITE8X8"
end

-- Check for conflicting addons
local function hasConflictingAddons()
    -- Only disable if user specifically wants to avoid conflicts
    if LortiUI.db and LortiUI.db.profile and not LortiUI.db.profile.forceButtonStyling then
        return IsAddOnLoaded("Masque") or IsAddOnLoaded("Dominos") or IsAddOnLoaded("Bartender4")
    end
    return false
end

-- Create backdrop for buttons
local function createBackdrop()
    local db = LortiUI.db.profile
    local bgfile, edgefile = "", ""
    
    if db.enableButtonShadow then 
        edgefile = MEDIA_TEXTURES.outerShadow 
    end
    if db.enableButtonBackground and db.useFlatButtonBackground then 
        bgfile = MEDIA_TEXTURES.buttonBackgroundFlat 
    end
    
    return {
        bgFile = bgfile,
        edgeFile = edgefile,
        tile = false,
        tileSize = 32,
        edgeSize = 5,
        insets = {
            left = 5,
            right = 5,
            top = 5,
            bottom = 5,
        },
    }
end

-- Apply background to button
local function applyBackground(button)
    if not button or button.lortiBackground then return end
    
    local db = LortiUI.db.profile
    if not (db.enableButtonBackground or db.enableButtonShadow) then return end
    
    -- Create background frame
    local bg = CreateFrame("Frame", nil, button)
    bg:SetPoint("TOPLEFT", button, "TOPLEFT", -4, 4)
    bg:SetPoint("BOTTOMRIGHT", button, "BOTTOMRIGHT", 4, -4)
    bg:SetFrameLevel(math.max(button:GetFrameLevel() - 1, 0))
    
    -- Apply backdrop
    bg:SetBackdrop(createBackdrop())
    
    if db.enableButtonBackground then
        if not db.useFlatButtonBackground then
            -- Textured background with fallback
            local texture = bg:CreateTexture(nil, "BACKGROUND", nil, -8)
            texture:SetTexture(getTexture("buttonBackground"))
            texture:SetAllPoints(button)
            local color = db.buttonBackgroundColor
            texture:SetVertexColor(color.r, color.g, color.b, color.a)
        else
            -- Flat color background
            local color = db.buttonBackgroundColor
            bg:SetBackdropColor(color.r, color.g, color.b, color.a)
        end
    end
    
    if db.enableButtonShadow then
        local color = db.buttonShadowColor
        bg:SetBackdropBorderColor(color.r, color.g, color.b, color.a)
    end
    
    button.lortiBackground = bg
end

-- Function to completely remove all styling from a button and restore original look
local function removeButtonStyling(button)
    if not button then return end

    local name = button:GetName()
    if not name then return end



    -- FORCE restore original textures to default WoW look
    if button.SetNormalTexture then
        button:SetNormalTexture("Interface\\Buttons\\UI-Quickslot2")
    end
    if button.SetHighlightTexture then
        button:SetHighlightTexture("Interface\\Buttons\\ButtonHilight-Square")
    end
    if button.SetPushedTexture then
        button:SetPushedTexture("Interface\\Buttons\\UI-Quickslot-Depress")
    end
    if button.SetCheckedTexture then
        button:SetCheckedTexture("Interface\\Buttons\\CheckButtonHilight")
    end

    -- FORCE restore normal texture properties
    local normalTexture = button:GetNormalTexture()
    if normalTexture then
        normalTexture:SetVertexColor(1, 1, 1, 1) -- Reset to white
        normalTexture:ClearAllPoints()
        normalTexture:SetAllPoints(button)
    end

    -- Restore icon positioning to EXACT WoW default
    local icon = _G[name.."Icon"]
    if icon then
        icon:SetTexCoord(0.07, 0.93, 0.07, 0.93) -- TRUE WoW default coordinates
        icon:ClearAllPoints()
        icon:SetPoint("TOPLEFT", button, "TOPLEFT", 0, 0)
        icon:SetPoint("BOTTOMRIGHT", button, "BOTTOMRIGHT", 0, 0)
    end

    -- Restore cooldown positioning to EXACT WoW default
    local cooldown = _G[name.."Cooldown"]
    if cooldown then
        cooldown:ClearAllPoints()
        cooldown:SetPoint("TOPLEFT", button, "TOPLEFT", 0, 0)
        cooldown:SetPoint("BOTTOMRIGHT", button, "BOTTOMRIGHT", 0, 0)
    end

    -- Restore flash texture to WoW default
    local flash = _G[name.."Flash"]
    if flash then
        flash:SetTexture("Interface\\Buttons\\UI-QuickslotRed")
        flash:SetTexCoord(0.07, 0.93, 0.07, 0.93) -- WoW default coordinates
        flash:ClearAllPoints()
        flash:SetPoint("TOPLEFT", button, "TOPLEFT", 0, 0)
        flash:SetPoint("BOTTOMRIGHT", button, "BOTTOMRIGHT", 0, 0)
    end

    -- Restore count and hotkey positioning
    local count = _G[name.."Count"]
    if count then
        count:ClearAllPoints()
        count:SetPoint("BOTTOMRIGHT", button, "BOTTOMRIGHT", -2, 2)
    end

    local hotkey = _G[name.."HotKey"]
    if hotkey then
        hotkey:ClearAllPoints()
        hotkey:SetPoint("TOPRIGHT", button, "TOPRIGHT", -2, -2)
    end

    -- Restore macro name
    local macroName = _G[name.."Name"]
    if macroName then
        macroName:ClearAllPoints()
        macroName:SetPoint("BOTTOM", button, "BOTTOM", 0, 2)
    end

    -- Restore border
    local border = _G[name.."Border"]
    if border then
        border.Show = nil -- Remove our override
        border:Show()
    end

    -- Restore normal texture color (CLEAN)
    local normalTexture = _G[name.."NormalTexture"]
    if normalTexture then
        normalTexture:SetVertexColor(1, 1, 1, 1) -- Reset to white with alpha
    end

    -- Also try to get normalTexture directly from button
    local buttonNormalTexture = button:GetNormalTexture()
    if buttonNormalTexture and buttonNormalTexture ~= normalTexture then
        buttonNormalTexture:SetVertexColor(1, 1, 1, 1)
    end

    -- Remove custom backgrounds and overlays (fixed for WoW 3.3.5a)
    if button.lortiBackground then
        button.lortiBackground:Hide()
        -- Don't use SetParent(nil) in WoW 3.3.5a
        button.lortiBackground = nil
    end
    if button.lortiShadow then
        button.lortiShadow:Hide()
        -- Don't use SetParent(nil) in WoW 3.3.5a
        button.lortiShadow = nil
    end

    -- Reset styling flag
    button.lortiStyled = nil

    -- AGGRESSIVE button update to ensure changes are visible
    local buttonName = button:GetName()
    if buttonName then
        -- Try multiple update methods
        if button.Update then
            pcall(button.Update, button)
        end
        if ActionButton_UpdateAction and buttonName:match("ActionButton") then
            pcall(ActionButton_UpdateAction, button)
        end
        if ActionButton_UpdateUsable and buttonName:match("ActionButton") then
            pcall(ActionButton_UpdateUsable, button)
        end
        if ActionButton_UpdateState and buttonName:match("ActionButton") then
            pcall(ActionButton_UpdateState, button)
        end
        -- Force redraw
        if button.SetAlpha then
            local alpha = button:GetAlpha()
            button:SetAlpha(alpha - 0.01)
            button:SetAlpha(alpha)
        end
    end
end

-- Style individual action button (simplified - called only when styling enabled)
local function styleActionButton(button)
    if not button then return end
    if not LortiUI or not LortiUI.db or not LortiUI.db.profile then return end

    -- Don't re-style if already styled
    if button.lortiStyled then return end

    local db = LortiUI.db.profile

    local name = button:GetName()
    if not name then return end
    
    -- Get button elements
    local icon = _G[name.."Icon"]
    local count = _G[name.."Count"]
    local border = _G[name.."Border"]
    local hotkey = _G[name.."HotKey"]
    local cooldown = _G[name.."Cooldown"]
    local macroName = _G[name.."Name"]
    local flash = _G[name.."Flash"]
    local normalTexture = _G[name.."NormalTexture"]
    
    -- Hide border
    if border then
        border:Hide()
        border.Show = function() end
    end
    
    -- Style icon
    if icon then
        icon:SetTexCoord(0.1, 0.9, 0.1, 0.9)
        icon:SetPoint("TOPLEFT", button, "TOPLEFT", 2, -2)
        icon:SetPoint("BOTTOMRIGHT", button, "BOTTOMRIGHT", -2, 2)
    end
    
    -- Style cooldown
    if cooldown then
        cooldown:SetPoint("TOPLEFT", button, "TOPLEFT", 2, -2)
        cooldown:SetPoint("BOTTOMRIGHT", button, "BOTTOMRIGHT", -2, 2)
    end
    
    -- Apply textures with fallbacks
    if flash then
        flash:SetTexture(getTexture("flash"))
        flash:SetTexCoord(0.1, 0.9, 0.1, 0.9)
    end

    -- Apply textures safely (only if button styling is enabled)
    if db.enableButtonStyling then
        if button.SetHighlightTexture then
            pcall(button.SetHighlightTexture, button, getTexture("hover"))
        end
        if button.SetPushedTexture then
            pcall(button.SetPushedTexture, button, getTexture("pushed"))
        end
        if button.SetCheckedTexture then
            pcall(button.SetCheckedTexture, button, getTexture("checked"))
        end
        if button.SetNormalTexture then
            pcall(button.SetNormalTexture, button, getTexture("gloss"))
        end

        -- Style normal texture
        if normalTexture then
            normalTexture:SetAllPoints(button)
            normalTexture:SetVertexColor(db.buttonNormalColor.r, db.buttonNormalColor.g, db.buttonNormalColor.b)
        end
    end

    -- Apply background (only if background is enabled)
    if db.enableButtonBackground then
        applyBackground(button)
    end
    
    -- Mark as styled
    button.lortiStyled = true
end

-- Function to remove styling from all buttons (ENHANCED)
function LortiUI:RemoveAllButtonStyling()
    -- Remove styling from standard action buttons (expanded list)
    local buttonPatterns = {
        "ActionButton", "MultiBarBottomLeftButton", "MultiBarBottomRightButton",
        "MultiBarLeftButton", "MultiBarRightButton", "BonusActionButton",
        "ExtraActionButton", "OverrideActionBarButton", "VehicleMenuBarActionButton"
    }
    for _, pattern in ipairs(buttonPatterns) do
        for i = 1, 12 do
            local button = _G[pattern..i]
            if button then
                removeButtonStyling(button)
            end
        end
    end

    -- Also check Bartender4 buttons
    for i = 1, 120 do
        local button = _G["BT4Button"..i]
        if button then
            removeButtonStyling(button)
        end
    end

    -- Remove styling from pet action buttons
    for i = 1, 10 do
        local button = _G["PetActionButton"..i]
        if button then
            removeButtonStyling(button)
        end
    end

    -- Remove styling from shapeshift buttons
    for i = 1, 10 do
        local button = _G["ShapeshiftButton"..i]
        if button then
            removeButtonStyling(button)
        end
    end

    -- Remove styling from Dominos buttons if present
    for i = 1, 120 do
        local button = _G["DominosActionButton"..i]
        if button then
            removeButtonStyling(button)
        end
    end

    -- AGGRESSIVE force update ALL action bars to ensure changes are visible
    -- Update main action bar
    for i = 1, 12 do
        local button = _G["ActionButton"..i]
        if button then
            if ActionButton_UpdateAction then pcall(ActionButton_UpdateAction, button) end
            if ActionButton_UpdateUsable then pcall(ActionButton_UpdateUsable, button) end
            if ActionButton_UpdateState then pcall(ActionButton_UpdateState, button) end
        end
    end

    -- Update all multi bars
    local barPatterns = {"MultiBarBottomLeftButton", "MultiBarBottomRightButton", "MultiBarLeftButton", "MultiBarRightButton"}
    for _, pattern in pairs(barPatterns) do
        for i = 1, 12 do
            local button = _G[pattern..i]
            if button and ActionButton_UpdateAction then
                pcall(ActionButton_UpdateAction, button)
            end
        end
    end

    -- Cleanup action bar textures (NO HOOKS TO REMOVE)
    local actionBarTextures = {
        "MainMenuBarTexture0", "MainMenuBarTexture1", "MainMenuBarTexture2", "MainMenuBarTexture3",
        "MainMenuBarLeftEndCap", "MainMenuBarRightEndCap",
        "MultiBarBottomLeftTexture0", "MultiBarBottomLeftTexture1",
        "MultiBarBottomRightTexture0", "MultiBarBottomRightTexture1",
        "MultiBarLeftTexture0", "MultiBarLeftTexture1",
        "MultiBarRightTexture0", "MultiBarRightTexture1"
    }

    for _, textureName in pairs(actionBarTextures) do
        local texture = _G[textureName]
        if texture and texture.SetVertexColor then
            texture:SetVertexColor(1, 1, 1, 1)
        end
    end

    -- Force UI refresh
    if UIParent then
        UIParent:SetAlpha(UIParent:GetAlpha())
    end
end

-- Style pet action buttons (SIMPLIFIED)
function LortiUI:StylePetActionButtons()
    for i = 1, 10 do
        local button = _G["PetActionButton"..i]
        if button and not button.lortiStyled then
            styleActionButton(button)
        end
    end
end

-- Style shapeshift buttons (SIMPLIFIED)
function LortiUI:StyleShapeshiftButtons()
    for i = 1, 10 do
        local button = _G["ShapeshiftButton"..i]
        if button and not button.lortiStyled then
            styleActionButton(button)
        end
    end
end

-- Style all action bar buttons (SIMPLIFIED)
function LortiUI:StyleAllActionButtons()
    self:StyleStandardActionBars()
    self:StyleDominosBars()
    self:StyleBartenderBars()
end

-- Style standard WoW action bars
function LortiUI:StyleStandardActionBars()
    local buttonPatterns = {
        "ActionButton",              -- Main action bar
        "MultiBarBottomLeftButton",  -- Bottom left bar
        "MultiBarBottomRightButton", -- Bottom right bar
        "MultiBarLeftButton",        -- Right side bar
        "MultiBarRightButton",       -- Far right side bar
        "BonusActionButton",         -- Bonus action bar
        "ExtraActionButton",         -- Extra action button
        "OverrideActionBarButton",   -- Override bar
        "VehicleMenuBarActionButton" -- Vehicle bar
    }

    for _, pattern in pairs(buttonPatterns) do
        for i = 1, 12 do
            local button = _G[pattern..i]
            if button and not button.lortiStyled then
                styleActionButton(button)
            end
        end
    end
end

-- Style Dominos addon bars (SIMPLIFIED)
function LortiUI:StyleDominosBars()
    for i = 1, 120 do
        local button = _G["DominosActionButton"..i]
        if button and not button.lortiStyled then
            styleActionButton(button)
        end
    end
end

-- Style Bartender addon bars (SIMPLIFIED)
function LortiUI:StyleBartenderBars()
    for i = 1, 120 do
        local button = _G["BT4Button"..i]
        if button and not button.lortiStyled then
            styleActionButton(button)
        end
    end
end

-- Initialize action bar styling (FIXED FORCE LOGIC)
function LortiUI:InitializeActionBarStyling()
    -- ALWAYS remove existing styling first
    self:RemoveAllButtonStyling()

    -- Skip applying NEW styling if conflicting addons are loaded AND force is disabled
    if hasConflictingAddons() then
        -- Just exit after cleanup - don't apply new styling
        return
    end

    -- Apply styling only if no conflicts or force is enabled
    self:StylePetActionButtons()
    self:StyleShapeshiftButtons()
    self:StyleAllActionButtons()
end

-- Clear styling from a button
local function clearButtonStyling(button)
    if button and button.lortiStyled then
        button.lortiStyled = nil
        if button.lortiBackground then
            button.lortiBackground:Hide()
            button.lortiBackground = nil
        end
    end
end

-- Update button styling when settings change (CLEAN)
function LortiUI:UpdateActionBarStyling()
    -- ALWAYS remove all existing styling first
    self:RemoveAllButtonStyling()

    -- If main styling is enabled, try to apply it
    if self.db.profile.enableButtonStyling then
        -- InitializeActionBarStyling will handle force logic internally
        self:InitializeActionBarStyling()
    end

    -- Always force UI refresh after changes
    C_Timer.After(0.1, function()
        if MainMenuBar and MainMenuBar.SetAlpha then
            local alpha = MainMenuBar:GetAlpha()
            MainMenuBar:SetAlpha(alpha - 0.01)
            MainMenuBar:SetAlpha(alpha)
        end
    end)
end