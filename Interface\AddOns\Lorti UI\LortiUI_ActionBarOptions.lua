-- LortiUI Action Bar Options
-- Separate file for action bar configuration

local LortiUI = LibStub("AceAddon-3.0"):GetAddon("LortiUI")
local L = LortiUI_Locale

-- Simple helper functions
local function createToggle(key, name, desc, order, disabled)
    return {
        type = "toggle",
        name = name,
        desc = desc,
        order = order,
        get = function(info)
            return LortiUI.db and LortiUI.db.profile and LortiUI.db.profile[key] or false
        end,
        set = function(info, value)
            if LortiUI.db and LortiUI.db.profile then
                LortiUI.db.profile[key] = value
                if LortiUI.UpdateActionBarStyling then
                    LortiUI:UpdateActionBarStyling()
                end
            end
        end,
        disabled = disabled,
    }
end

local function createColorOption(key, name, desc, order, hasAlpha, disabled)
    return {
        type = "color",
        name = name,
        desc = desc,
        order = order,
        hasAlpha = hasAlpha,
        get = function(info)
            if not LortiUI.db or not LortiUI.db.profile then
                return 0, 0, 0, 1
            end
            local color = LortiUI.db.profile[key] or {r = 0, g = 0, b = 0, a = 1}
            if hasAlpha then
                return color.r or 0, color.g or 0, color.b or 0, color.a or 1
            else
                return color.r or 0, color.g or 0, color.b or 0
            end
        end,
        set = function(info, r, g, b, a)
            if LortiUI.db and LortiUI.db.profile then
                if hasAlpha then
                    LortiUI.db.profile[key] = {r = r, g = g, b = b, a = a}
                else
                    LortiUI.db.profile[key] = {r = r, g = g, b = b}
                end
                if LortiUI.UpdateActionBarStyling then
                    LortiUI:UpdateActionBarStyling()
                end
            end
        end,
        disabled = disabled,
    }
end

-- Tab 2: Action Bars
local function CreateActionBarsTab()
    return {
        type = "group",
        name = L["Action Bars"] or "Action Bars",
        order = 2,
        args = {
            description = {
                type = "description",
                name = L["Customize the appearance of action bar buttons"] or "Customize the appearance of action bar buttons",
                order = 1,
            },

            -- Main Settings
            mainHeader = {
                type = "header",
                name = L["Main Settings"] or "Main Settings",
                order = 10,
            },
            enableButtonStyling = createToggle("enableButtonStyling", L["Enable Button Styling"] or "Enable Button Styling", L["Enable styling for action bar buttons"] or "Enable styling for action bar buttons", 11),

            -- Styling Options
            stylingHeader = {
                type = "header",
                name = L["Styling Options"] or "Styling Options",
                order = 20,
            },
            enableButtonBackground = createToggle("enableButtonBackground", L["Enable Button Background"] or "Enable Button Background", L["Show background texture on buttons"] or "Show background texture on buttons", 21,
                function() return not LortiUI.db.profile.enableButtonStyling end),
            enableButtonShadow = createToggle("enableButtonShadow", L["Enable Button Shadow"] or "Enable Button Shadow", L["Show shadow around buttons"] or "Show shadow around buttons", 22,
                function() return not LortiUI.db.profile.enableButtonStyling end),
            useFlatButtonBackground = createToggle("useFlatButtonBackground", L["Use Flat Background"] or "Use Flat Background", L["Use flat color instead of textured background"] or "Use flat color instead of textured background", 23,
                function() return not LortiUI.db.profile.enableButtonStyling or not LortiUI.db.profile.enableButtonBackground end),

            -- Addon Compatibility
            compatibilityHeader = {
                type = "header",
                name = L["Addon Compatibility"] or "Addon Compatibility",
                order = 30,
            },
            styleStandardBars = createToggle("styleStandardBars", L["Style Standard Bars"] or "Style Standard Bars", L["Style default WoW action bars"] or "Style default WoW action bars", 31,
                function() return not LortiUI.db.profile.enableButtonStyling end),
            styleDominosBars = createToggle("styleDominosBars", L["Style Dominos Bars"] or "Style Dominos Bars", L["Style Dominos addon bars (if installed)"] or "Style Dominos addon bars (if installed)", 32,
                function() return not LortiUI.db.profile.enableButtonStyling end),
            styleBartenderBars = createToggle("styleBartenderBars", L["Style Bartender Bars"] or "Style Bartender Bars", L["Style Bartender addon bars (if installed)"] or "Style Bartender addon bars (if installed)", 33,
                function() return not LortiUI.db.profile.enableButtonStyling end),
            styleOtherAddonBars = createToggle("styleOtherAddonBars", L["Style Other Addon Bars"] or "Style Other Addon Bars", L["Style bars from other addons"] or "Style bars from other addons", 34,
                function() return not LortiUI.db.profile.enableButtonStyling end),

            -- Advanced Options
            advancedHeader = {
                type = "header",
                name = L["Advanced Options"] or "Advanced Options",
                order = 40,
            },
            forceButtonStyling = createToggle("forceButtonStyling", L["Force Button Styling"] or "Force Button Styling", L["Force styling even with conflicting addons (may cause issues)"] or "Force styling even with conflicting addons (may cause issues)", 41,
                function() return not LortiUI.db.profile.enableButtonStyling end),
            enableUniversalStyling = createToggle("enableUniversalStyling", L["Enable Universal Styling"] or "Enable Universal Styling", L["Style ALL buttons, not just action bars (experimental, may cause lag)"] or "Style ALL buttons, not just action bars (experimental, may cause lag)", 42,
                function() return not LortiUI.db.profile.enableButtonStyling end),

            -- Color Options
            colorHeader = {
                type = "header",
                name = L["Color Options"] or "Color Options",
                order = 50,
            },
            buttonBackgroundColor = createColorOption("buttonBackgroundColor", L["Button Background Color"] or "Button Background Color", L["Color for button backgrounds"] or "Color for button backgrounds", 51, true,
                function() return not LortiUI.db.profile.enableButtonStyling or not LortiUI.db.profile.enableButtonBackground end),
            buttonShadowColor = createColorOption("buttonShadowColor", L["Button Shadow Color"] or "Button Shadow Color", L["Color for button shadows"] or "Color for button shadows", 52, true,
                function() return not LortiUI.db.profile.enableButtonStyling or not LortiUI.db.profile.enableButtonShadow end),
            buttonNormalColor = createColorOption("buttonNormalColor", L["Button Normal Color"] or "Button Normal Color", L["Color for normal button state"] or "Color for normal button state", 53, false,
                function() return not LortiUI.db.profile.enableButtonStyling end),
        }
    }
end

-- Export the function
LortiUI.CreateActionBarsTab = CreateActionBarsTab
