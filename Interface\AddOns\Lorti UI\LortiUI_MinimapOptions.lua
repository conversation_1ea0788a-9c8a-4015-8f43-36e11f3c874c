-- LortiUI Minimap Options
-- Separate file for minimap configuration

local LortiUI = LibStub("AceAddon-3.0"):GetAddon("LortiUI")
local L = LortiUI_Locale

-- Simple helper functions
local function createToggle(key, name, desc, order, disabled)
    return {
        type = "toggle",
        name = name,
        desc = desc,
        order = order,
        get = function(info) return LortiUI.db.profile[key] end,
        set = function(info, value)
            LortiUI.db.profile[key] = value
            if LortiUI.InitializeMinimapEnhancements then
                LortiUI:InitializeMinimapEnhancements()
            end
        end,
        disabled = disabled,
    }
end

local function createRange(key, name, desc, order, min, max, step, disabled)
    return {
        type = "range",
        name = name,
        desc = desc,
        order = order,
        min = min,
        max = max,
        step = step,
        get = function(info) return LortiUI.db.profile[key] end,
        set = function(info, value)
            LortiUI.db.profile[key] = value
            if Lorti<PERSON>.InitializeMinimapEnhancements then
                LortiUI:InitializeMinimapEnhancements()
            end
        end,
        disabled = disabled,
    }
end

-- Tab 4: Minimap
local function CreateMinimapTab()
    return {
        type = "group",
        name = L["Minimap"] or "Minimap",
        order = 4,
        args = {
            description = {
                type = "description",
                name = L["Customize minimap appearance and behavior"] or "Customize minimap appearance and behavior",
                order = 1,
            },

            -- Minimap Enhancements
            enhancementsHeader = {
                type = "header",
                name = L["Minimap Enhancements"] or "Minimap Enhancements",
                order = 10,
            },
            enableMinimapEnhancements = createToggle("enableMinimapEnhancements", L["Enable Minimap Enhancements"] or "Enable Minimap Enhancements", L["Enable minimap visual enhancements"] or "Enable minimap visual enhancements", 11),
            
            -- Size and Scale
            sizeHeader = {
                type = "header",
                name = L["Size and Scale"] or "Size and Scale",
                order = 20,
            },
            minimapScale = createRange("minimapScale", L["Minimap Scale"] or "Minimap Scale", L["Scale of the minimap"] or "Scale of the minimap", 21, 0.5, 2.0, 0.05,
                function() return not LortiUI.db.profile.enableMinimapEnhancements end),

            -- Border and Background
            borderHeader = {
                type = "header",
                name = L["Border and Background"] or "Border and Background",
                order = 30,
            },
            enableMinimapBorder = createToggle("enableMinimapBorder", L["Enable Minimap Border"] or "Enable Minimap Border", L["Show border around minimap"] or "Show border around minimap", 31,
                function() return not LortiUI.db.profile.enableMinimapEnhancements end),
            enableMinimapBackground = createToggle("enableMinimapBackground", L["Enable Minimap Background"] or "Enable Minimap Background", L["Show background behind minimap"] or "Show background behind minimap", 32,
                function() return not LortiUI.db.profile.enableMinimapEnhancements end),

            -- Transparency
            transparencyHeader = {
                type = "header",
                name = L["Transparency"] or "Transparency",
                order = 40,
            },
            minimapTransparency = createRange("minimapTransparency", L["Minimap Transparency"] or "Minimap Transparency", L["Transparency level for minimap"] or "Transparency level for minimap", 41, 0.1, 1.0, 0.01,
                function() return not LortiUI.db.profile.enableMinimapEnhancements end),

            -- Position
            positionHeader = {
                type = "header",
                name = L["Position"] or "Position",
                order = 50,
            },
            enableMinimapMove = createToggle("enableMinimapMove", L["Enable Minimap Move"] or "Enable Minimap Move", L["Allow moving the minimap"] or "Allow moving the minimap", 51,
                function() return not LortiUI.db.profile.enableMinimapEnhancements end),


        }
    }
end

-- Export the function
LortiUI.CreateMinimapTab = CreateMinimapTab
