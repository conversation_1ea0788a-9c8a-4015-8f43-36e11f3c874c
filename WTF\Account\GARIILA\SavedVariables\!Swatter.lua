
SwatterData = {
	["enabled"] = true,
	["autoshow"] = true,
	["errors"] = {
		{
			["message"] = "WTF\\Account\\GARIILA\\Icecrown\\Anelny\\SavedVariables\\SkadaStorage.lua:281538: ']' expected near '<eof>'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=763)\n",
			["timestamp"] = "2025-07-11 01:46:00",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [1]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:66: attempt to index global 'C_AddOns' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.0\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 01:52:20",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:66: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [2]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:66: attempt to index global 'C_AddOns' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.0\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 01:52:57",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:66: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [3]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:645: attempt to concatenate a nil value",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 02:12:33",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:645: in function `CreateCheckButton'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:92: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [4]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:645: attempt to concatenate a nil value",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 02:13:12",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:645: in function `CreateCheckButton'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:92: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [5]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:645: attempt to concatenate a nil value",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 02:15:29",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:645: in function `CreateCheckButton'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:92: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [6]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:656: attempt to concatenate a nil value",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 02:20:06",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:656: in function `CreateCheckButton'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:103: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [7]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:126: attempt to concatenate a nil value",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 02:21:32",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:126: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [8]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:617: attempt to index global 'showHPDesc' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 02:24:26",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:617: in function `CreateConfigMenu'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:19: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [9]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:184: stack overflow",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-11 03:02:18",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `GetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:184: in function `HandleBarColor'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:125: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:137: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:38: in function `InitializeBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:25: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:24>\n(tail call): ?\n",
		}, -- [10]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: stack overflow",
			["count"] = 6,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-11 03:02:40",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:286: in function `SetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:180: in function `SetLock'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:134: in function `ShowOrHideBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:128: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:342: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:340>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:68: in function <...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:57>\n",
		}, -- [11]
		{
			["message"] = "WTF\\Account\\GARIILA\\Icecrown\\Anelny\\SavedVariables\\Spy.lua:11120: unexpected symbol near '<eof>'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=797)\n",
			["timestamp"] = "2025-07-11 03:07:34",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [12]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:189: attempt to index global 'options' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 03:16:52",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:189: in main chunk\n",
		}, -- [13]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:50: attempt to call field 'GetConfigValue' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=7b3)\n",
			["timestamp"] = "2025-07-11 03:16:52",
			["context"] = "Global",
			["stack"] = "[C]: in function `GetConfigValue'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:50: in function `CreatethpBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:18: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:11>\n(tail call): ?\n",
		}, -- [14]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:36: attempt to call field 'GetConfigValue' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-11 03:16:53",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `GetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:36: in function `InitializeBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:25: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:24>\n(tail call): ?\n",
		}, -- [15]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:95: Usage: <unnamed>:SetAlpha(alpha)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:28:23",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: in function `SetAlpha'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:95: in function `HandleBarColor'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:88: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:66: in function `UpdateBar'\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:43: in function <...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:31>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[4]\"]:9: in function <[string \"safecall Dispatcher[4]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:22: in function <...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:19>\n",
		}, -- [16]
		{
			["message"] = "...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:229: attempt to index local 'c' (a nil value)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:28:23",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:229: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1352: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:848: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[4]\"]:9: in function <[string \"safecall Dispatcher[4]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:22: in function <...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:19>\n",
		}, -- [17]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:105: Usage: <unnamed>:SetAlpha(alpha)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:30:57",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: in function `SetAlpha'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:105: in function `HandleBarColor'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:97: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:66: in function `UpdateBar'\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:43: in function <...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:31>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[4]\"]:9: in function <[string \"safecall Dispatcher[4]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:22: in function <...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:19>\n",
		}, -- [18]
		{
			["message"] = "...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:229: attempt to index local 'c' (a nil value)",
			["count"] = 529,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:30:57",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:229: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1352: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:848: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[4]\"]:9: in function <[string \"safecall Dispatcher[4]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:22: in function <...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:19>\n",
		}, -- [19]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:27: attempt to index field 'barPos' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:31:24",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:27: in function <...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:22>\n",
		}, -- [20]
		{
			["message"] = "...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:105: Usage: <unnamed>:SetAlpha(alpha)",
			["count"] = 359,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:32:40",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: in function `SetAlpha'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:105: in function `HandleBarColor'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:97: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:66: in function `UpdateBar'\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:43: in function <...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:31>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[4]\"]:9: in function <[string \"safecall Dispatcher[4]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:22: in function <...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:19>\n",
		}, -- [21]
		{
			["message"] = "...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:229: attempt to index local 'c' (a nil value)",
			["count"] = 313,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 05:32:40",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:229: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1352: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:848: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[4]\"]:9: in function <[string \"safecall Dispatcher[4]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:22: in function <...mpat\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-Button.lua:19>\n",
		}, -- [22]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:262: attempt to index field 'db' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-12 18:14:04",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:262: in function `GetConfigValue'\n...AddOns\\TargetHealthPercent\\TargetHealthPercentUI.lua:35: in function `CreatethpBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:51: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:44>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:9: in function <[string \"safecall Dispatcher[1]\"]:5>\n(tail call): ?\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:558: in function `EnableAddon'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:651: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:636>\n[C]: in function `LoadAddOn'\nInterface\\FrameXML\\UIParent.lua:235: in function `UIParentLoadAddOn'\nInterface\\FrameXML\\UIParent.lua:258: in function `CombatLog_LoadUI'\nInterface\\FrameXML\\UIParent.lua:482: in function <Interface\\FrameXML\\UIParent.lua:454>\n",
		}, -- [23]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:266: attempt to index field 'db' (a nil value)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.1\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-12 18:19:59",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:266: in function `GetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:375: in function `HandleBarColor'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:155: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:84: in function `OnUpdate'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:73: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:72>\n",
		}, -- [24]
		{
			["message"] = "WTF\\Account\\GARIILA\\Icecrown\\Anelny\\SavedVariables\\SkadaStorage.lua:83873: unexpected symbol near '<eof>'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  TomTom, v237\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=763)\n",
			["timestamp"] = "2025-07-12 19:02:40",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [25]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:351: attempt to index field 'customDB' (a nil value)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.2\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-12 19:02:45",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:351: in function `GetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:472: in function `HandleBarColor'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:226: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:155: in function `OnUpdate'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:144: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:143>\n",
		}, -- [26]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:351: attempt to index field 'customDB' (a nil value)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.2\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-12 19:03:35",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:351: in function `GetConfigValue'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:472: in function `HandleBarColor'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:226: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:155: in function `OnUpdate'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:144: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:143>\n",
		}, -- [27]
		{
			["message"] = "...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:462: Usage: TargetHealthPercentBarBackground:SetAlpha(alpha)",
			["count"] = 1000,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.2\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9f3)\n",
			["timestamp"] = "2025-07-12 21:01:01",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: in function `SetAlpha'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:462: in function `HandleBarColor'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:171: in function `UpdateBar'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:96: in function `OnUpdate'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:84: in function <...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:82>\n",
		}, -- [28]
		{
			["message"] = "...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:178: attempt to index upvalue 'L' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.4\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeed, v1.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.2\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9b9)\n",
			["timestamp"] = "2025-07-12 22:31:49",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:178: in function `recreateOptions'\n...s\\TargetHealthPercent\\TargetHealthPercent_Config.lua:305: in function `InitializeConfig'\n...e\\AddOns\\TargetHealthPercent\\TargetHealthPercent.lua:62: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [29]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:110: '=' expected near 'continue'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-07-13 00:25:05",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [30]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:110: '=' expected near 'continue'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-07-13 00:27:02",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [31]
		{
			["message"] = "Interface\\AddOns\\LootLog\\LootLog.lua:115: '=' expected near 'continue'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.4\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=518)\n",
			["timestamp"] = "2025-07-13 00:29:02",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [32]
		{
			["message"] = "...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:126: attempt to index global 'MouseSpeedEnhanced' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeedEnhanced, v2.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=57d)\n",
			["timestamp"] = "2025-07-13 16:40:37",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:126: in main chunk\n",
		}, -- [33]
		{
			["message"] = "...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:126: attempt to index global 'MouseSpeedEnhanced' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCam, v1.0\n  MouseSpeedEnhanced, v2.0.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=57f)\n",
			["timestamp"] = "2025-07-13 16:41:26",
			["context"] = "Global",
			["stack"] = "[C]: ?\n...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:126: in main chunk\n",
		}, -- [34]
		{
			["message"] = "Interface\\AddOns\\Skada\\Modules\\Enemies.lua:476: invalid option in `format'",
			["count"] = 32,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMChamberOfAspects, v\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  ScrapMerchant, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a31)\n",
			["timestamp"] = "2025-07-13 22:31:49",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: ?\nInterface\\AddOns\\Skada\\Modules\\Enemies.lua:476: in function `post_tooltip'\nInterface\\AddOns\\Skada\\Core\\Core.lua:1532: in function `ShowTooltip'\nInterface\\AddOns\\Skada\\Core\\Display\\Bar.lua:236: in function `?'\n...ter\\Libs\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:147: in function <...ter\\Libs\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:147>\n[string \"safecall Dispatcher[3]\"]:4: in function <[string \"safecall Dispatcher[3]\"]:4>\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:13: in function `?'\n...ter\\Libs\\CallbackHandler-1.0\\CallbackHandler-1.0.lua:92: in function `Fire'\n...bs\\SpecializedLibBars-1.0\\SpecializedLibBars-1.0.lua:1740: in function <...bs\\SpecializedLibBars-1.0\\SpecializedLibBars-1.0.lua:1734>\n",
		}, -- [35]
		{
			["message"] = "[string \"SetCameraDistance(30)\"]:1: attempt to call global 'SetCameraDistance' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-13 23:13:43",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetCameraDistance'\n[string \"SetCameraDistance(30)\"]:1: in main chunk\n[C]: in function `RunScript'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:393: in function `?'\n...ddOns\\Chatter\\Libs\\AceConsole-3.0\\AceConsole-3.0.lua:94: in function `value'\nInterface\\FrameXML\\ChatFrame.lua:4070: in function `ChatEdit_ParseText'\nInterface\\FrameXML\\ChatFrame.lua:3660: in function `ChatEdit_SendText'\nInterface\\FrameXML\\ChatFrame.lua:3698: in function `ChatEdit_OnEnterPressed'\n[string \"*:OnEnterPressed\"]:1: in function <[string \"*:OnEnterPressed\"]:1>\n",
		}, -- [36]
		{
			["message"] = "[string \"SetCameraDistance(10)\"]:1: attempt to call global 'SetCameraDistance' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-13 23:14:47",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetCameraDistance'\n[string \"SetCameraDistance(10)\"]:1: in main chunk\n[C]: in function `RunScript'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:393: in function `?'\n...ddOns\\Chatter\\Libs\\AceConsole-3.0\\AceConsole-3.0.lua:94: in function `?'\nInterface\\FrameXML\\ChatFrame.lua:4049: in function `ChatEdit_ParseText'\nInterface\\FrameXML\\ChatFrame.lua:3660: in function `ChatEdit_SendText'\nInterface\\FrameXML\\ChatFrame.lua:3698: in function `ChatEdit_OnEnterPressed'\n[string \"*:OnEnterPressed\"]:1: in function <[string \"*:OnEnterPressed\"]:1>\n",
		}, -- [37]
		{
			["message"] = "[string \"SetCameraDistance(20)\"]:1: attempt to call global 'SetCameraDistance' (a nil value)",
			["count"] = 2,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-13 23:14:55",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetCameraDistance'\n[string \"SetCameraDistance(20)\"]:1: in main chunk\n[C]: in function `RunScript'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:393: in function `?'\n...ddOns\\Chatter\\Libs\\AceConsole-3.0\\AceConsole-3.0.lua:94: in function `?'\nInterface\\FrameXML\\ChatFrame.lua:4049: in function `ChatEdit_ParseText'\nInterface\\FrameXML\\ChatFrame.lua:3660: in function `ChatEdit_SendText'\nInterface\\FrameXML\\ChatFrame.lua:3698: in function `ChatEdit_OnEnterPressed'\n[string \"*:OnEnterPressed\"]:1: in function <[string \"*:OnEnterPressed\"]:1>\n",
		}, -- [38]
		{
			["message"] = "[string \"SetCameraDistance(15)\"]:1: attempt to call global 'SetCameraDistance' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-13 23:15:04",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetCameraDistance'\n[string \"SetCameraDistance(15)\"]:1: in main chunk\n[C]: in function `RunScript'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:393: in function `?'\n...ddOns\\Chatter\\Libs\\AceConsole-3.0\\AceConsole-3.0.lua:94: in function `?'\nInterface\\FrameXML\\ChatFrame.lua:4049: in function `ChatEdit_ParseText'\nInterface\\FrameXML\\ChatFrame.lua:3660: in function `ChatEdit_SendText'\nInterface\\FrameXML\\ChatFrame.lua:3698: in function `ChatEdit_OnEnterPressed'\n[string \"*:OnEnterPressed\"]:1: in function <[string \"*:OnEnterPressed\"]:1>\n",
		}, -- [39]
		{
			["message"] = "Interface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:115: attempt to call global 'GetCameraZoom' (a nil value)",
			["count"] = 2,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-14 00:45:17",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:115: in function `AutoDetectOptimalDistance'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:166: in function `ApplyCameraSettings'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:207: in function `ApplySettings'\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:93: in function <...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:89>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:68: in function <...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:57>\n",
		}, -- [40]
		{
			["message"] = "...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:52: attempt to call global 'getGeneralOptions' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-14 01:37:10",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `getGeneralOptions'\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:52: in function <...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:37>\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:442: in function `InitializeConfig'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:107: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [41]
		{
			["message"] = "...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:52: attempt to call global 'getGeneralOptions' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-14 01:43:41",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `getGeneralOptions'\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:52: in function <...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:37>\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:445: in function `InitializeConfig'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:107: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [42]
		{
			["message"] = "...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:162: attempt to concatenate local 'cameraSpeed' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-14 01:43:52",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:162: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:371: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:344>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1079: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1924: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1922>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:20: in function <...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:19>\n[C]: in function `Show'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:28: in function `InterfaceOptionsList_DisplayPanel'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:46: in function `InterfaceOptionsListButton_OnClick'\n[string \"*:OnClick\"]:2: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [43]
		{
			["message"] = "...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:52: attempt to call global 'getGeneralOptions' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-14 01:44:28",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `getGeneralOptions'\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:52: in function <...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:37>\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:445: in function `InitializeConfig'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:107: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [44]
		{
			["message"] = "...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:162: attempt to concatenate local 'cameraSpeed' (a nil value)",
			["count"] = 3,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-14 01:48:20",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:162: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:371: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:344>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1079: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1924: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1922>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:20: in function <...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:19>\n[C]: in function `Show'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:28: in function `InterfaceOptionsList_DisplayPanel'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:46: in function `InterfaceOptionsListButton_OnClick'\n[string \"*:OnClick\"]:2: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [45]
		{
			["message"] = "...ace\\AddOns\\MouseSpeedEnhanced\\MouseSpeedEnhanced.lua:124: attempt to index global 'io' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-14 03:03:23",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...ace\\AddOns\\MouseSpeedEnhanced\\MouseSpeedEnhanced.lua:124: in function `ReadWTFValues'\n...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:173: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:371: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:344>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1079: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1924: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1922>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:20: in function <...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:19>\n[C]: in function `Show'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:28: in function `InterfaceOptionsList_DisplayPanel'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:46: in function `InterfaceOptionsListButton_OnClick'\n[string \"*:OnClick\"]:2: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [46]
		{
			["message"] = "...ace\\AddOns\\MouseSpeedEnhanced\\MouseSpeedEnhanced.lua:124: attempt to index global 'io' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-14 03:05:43",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n...ace\\AddOns\\MouseSpeedEnhanced\\MouseSpeedEnhanced.lua:124: in function `ReadWTFValues'\n...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:164: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:371: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:344>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1079: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1924: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1922>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:20: in function <...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:19>\n[C]: in function `Show'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:28: in function `InterfaceOptionsList_DisplayPanel'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:46: in function `InterfaceOptionsListButton_OnClick'\n[string \"*:OnClick\"]:2: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [47]
		{
			["message"] = "AceLocale-3.0: MouseSpeedEnhanced: Missing entry for 'Advanced mouse and camera sensitivity control'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-14 22:01:46",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...ddOns\\AtlasLoot\\Libs\\AceLocale-3.0\\AceLocale-3.0.lua:31: in function <...ddOns\\AtlasLoot\\Libs\\AceLocale-3.0\\AceLocale-3.0.lua:29>\n...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:85: in function <...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:77>\n...Ons\\MouseSpeedEnhanced\\MouseSpeedEnhanced_Config.lua:281: in function `InitializeConfig'\n...ace\\AddOns\\MouseSpeedEnhanced\\MouseSpeedEnhanced.lua:70: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [48]
		{
			["message"] = "...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:594: attempt to call global 'createOptionsTable' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=56a)\n",
			["timestamp"] = "2025-07-15 02:30:05",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:594: in function `GetOptionsTable'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:91: in function <Interface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:60>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:9: in function <[string \"safecall Dispatcher[1]\"]:5>\n(tail call): ?\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:529: in function `InitializeAddon'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:644: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:636>\n",
		}, -- [49]
		{
			["message"] = "...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:594: attempt to call global 'createOptionsTable' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=56a)\n",
			["timestamp"] = "2025-07-15 02:31:57",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:594: in function `GetOptionsTable'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:91: in function <Interface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:60>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:9: in function <[string \"safecall Dispatcher[1]\"]:5>\n(tail call): ?\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:529: in function `InitializeAddon'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:644: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:636>\n",
		}, -- [50]
		{
			["message"] = "Interface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:155: attempt to call method 'LoadUnifiedSettings' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 02:40:26",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:155: in function `?'\nInterface\\AddOns\\Atlas\\Libs\\CallbackHandler-1.0.lua:146: in function <Interface\\AddOns\\Atlas\\Libs\\CallbackHandler-1.0.lua:146>\n[string \"safecall Dispatcher[1]\"]:4: in function <[string \"safecall Dispatcher[1]\"]:4>\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:13: in function `?'\nInterface\\AddOns\\Atlas\\Libs\\CallbackHandler-1.0.lua:91: in function `Fire'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceEvent-3.0\\AceEvent-3.0.lua:120: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceEvent-3.0\\AceEvent-3.0.lua:119>\n",
		}, -- [51]
		{
			["message"] = "...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:576: MaxCamEnhanced has already been added to the Blizzard Options Window with the given path",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 02:40:26",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1981: in function `AddToBlizOptions'\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:576: in function `InitializeConfig'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:139: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [52]
		{
			["message"] = "...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:576: MaxCamEnhanced has already been added to the Blizzard Options Window with the given path",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 02:48:11",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1981: in function `AddToBlizOptions'\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:576: in function `InitializeConfig'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:139: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [53]
		{
			["message"] = "...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:576: MaxCamEnhanced has already been added to the Blizzard Options Window with the given path",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 02:49:23",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1981: in function `AddToBlizOptions'\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:576: in function `InitializeConfig'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:139: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [54]
		{
			["message"] = "...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:574: attempt to call method 'GetOptionsTable' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 02:52:58",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `GetOptionsTable'\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:574: in function `InitializeConfig'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:135: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [55]
		{
			["message"] = "...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:574: attempt to call method 'GetOptionsTable' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 02:53:28",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `GetOptionsTable'\n...face\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced_Config.lua:574: in function `InitializeConfig'\nInterface\\AddOns\\MaxCamEnhanced\\MaxCamEnhanced.lua:135: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [56]
		{
			["message"] = "Interface\\AddOns\\HideFrame\\HideFrame.lua:148: attempt to call method 'RegisterChatCommand' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v1.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=4f9)\n",
			["timestamp"] = "2025-07-15 13:28:17",
			["context"] = "Global",
			["stack"] = "(tail call): ?\nInterface\\AddOns\\HideFrame\\HideFrame.lua:148: in function `SetupConfig'\nInterface\\AddOns\\HideFrame\\HideFrame.lua:20: in function <Interface\\AddOns\\HideFrame\\HideFrame.lua:15>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:9: in function <[string \"safecall Dispatcher[1]\"]:5>\n(tail call): ?\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:529: in function `InitializeAddon'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:644: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:636>\n",
		}, -- [57]
		{
			["message"] = "Interface\\AddOns\\HideFrame\\HideFrame.lua:36: attempt to call global 'HideDragonTexture' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v1.6.4\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 14:17:57",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\nInterface\\AddOns\\HideFrame\\HideFrame.lua:36: in function `ApplySettings'\nInterface\\AddOns\\HideFrame\\HideFrame.lua:30: in function <Interface\\AddOns\\HideFrame\\HideFrame.lua:28>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:9: in function <[string \"safecall Dispatcher[1]\"]:5>\n(tail call): ?\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:558: in function `EnableAddon'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:651: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:636>\n[C]: in function `LoadAddOn'\nInterface\\FrameXML\\UIParent.lua:235: in function `UIParentLoadAddOn'\nInterface\\FrameXML\\UIParent.lua:258: in function `CombatLog_LoadUI'\nInterface\\FrameXML\\UIParent.lua:482: in function <Interface\\FrameXML\\UIParent.lua:454>\n",
		}, -- [58]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Media.lua:217: <unnamed>:SetParent(): Cannot set a 'nil' parent for fonts or textures",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v2.1.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 15:47:38",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: in function `SetParent'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:217: in function <Interface\\AddOns\\Lorti UI\\LortiUI_Media.lua:215>\n",
		}, -- [59]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Media.lua:217: <unnamed>:SetParent(): Cannot set a 'nil' parent for fonts or textures",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v2.1.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 15:48:12",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: in function `SetParent'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:217: in function <Interface\\AddOns\\Lorti UI\\LortiUI_Media.lua:215>\n",
		}, -- [60]
		{
			["message"] = "C stack overflow",
			["count"] = 547,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 16:42:03",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n...\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: ?\n[C]: in function `ActionButton_Update'\nInterface\\FrameXML\\ActionButton.lua:360: in function `ActionButton_OnEvent'\n[string \"*:OnEvent\"]:1: in function <[string \"*:OnEvent\"]:1>\n",
		}, -- [61]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:145: attempt to call method 'SetCheckedTexture' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 16:54:21",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: in function `SetCheckedTexture'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:145: in function <Interface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:101>\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:384: in function `FindAndStyleAllButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:294: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [62]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:252: function arguments expected near 'and'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=52c)\n",
			["timestamp"] = "2025-07-15 16:56:41",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [63]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:252: function arguments expected near 'and'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=52c)\n",
			["timestamp"] = "2025-07-15 16:58:03",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [64]
		{
			["message"] = "...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: LibBabble-Zone-3.0: Translation \"GetObjectType\" not found.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 16:59:07",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:25: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:23>\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:28>\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:249: in function `StyleAllActionButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:308: in function `InitializeActionBarStyling'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:236: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [65]
		{
			["message"] = "...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: LibBabble-Zone-3.0: Translation \"GetObjectType\" not found.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 17:01:04",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:25: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:23>\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:28>\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:249: in function `StyleAllActionButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:306: in function `InitializeActionBarStyling'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:236: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [66]
		{
			["message"] = "...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: LibBabble-Zone-3.0: Translation \"GetObjectType\" not found.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 17:04:09",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:25: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:23>\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:28>\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:252: in function `StyleAllActionButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:305: in function `InitializeActionBarStyling'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:236: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [67]
		{
			["message"] = "...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: LibBabble-Zone-3.0: Translation \"GetObjectType\" not found.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-15 17:05:06",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:25: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:23>\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:28>\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:285: in function `StyleAllActionButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:338: in function `InitializeActionBarStyling'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:236: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [68]
		{
			["message"] = "...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1831: AceConfigRegistry-3.0:ValidateOptionsTable(): LortiUI.args.colorOptionsHeader.name: expected a string or funcref, got 'nil'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 17:15:57",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: ?\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:50: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:45>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:210: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:205>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:244: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:219>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:254: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:219>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:282: in function `ValidateOptionsTable'\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:299: in function `app'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1831: in function `Open'\n...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1842: in function <...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1840>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:20: in function <...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:19>\n[C]: in function `Show'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:28: in function `InterfaceOptionsList_DisplayPanel'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:46: in function `InterfaceOptionsListButton_OnClick'\n[string \"*:OnClick\"]:2: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [69]
		{
			["message"] = "...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1831: AceConfigRegistry-3.0:ValidateOptionsTable(): LortiUI.args.colorOptionsHeader.name: expected a string or funcref, got 'nil'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 17:16:31",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: ?\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:50: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:45>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:210: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:205>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:244: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:219>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:254: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:219>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:282: in function `ValidateOptionsTable'\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:299: in function `app'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1831: in function `Open'\n...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1842: in function <...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1840>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:20: in function <...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:19>\n[C]: in function `Show'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:28: in function `InterfaceOptionsList_DisplayPanel'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:46: in function `InterfaceOptionsListButton_OnClick'\n[string \"*:OnClick\"]:2: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [70]
		{
			["message"] = "...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: LibBabble-Zone-3.0: Translation \"GetObjectType\" not found.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 17:20:26",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:25: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:23>\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:28>\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:337: in function `StyleUniversalButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:227: in function `StyleAllActionButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:408: in function `InitializeActionBarStyling'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:493: in function `UpdateActionBarStyling'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:533: in function <Interface\\AddOns\\Lorti UI\\Lorti UI.lua:530>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:68: in function <...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:57>\n",
		}, -- [71]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Options.lua:5: Usage: GetLocale(application[, silent]): 'application' - No locales registered for 'LortiUI'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=52c)\n",
			["timestamp"] = "2025-07-15 17:52:27",
			["context"] = "Global",
			["stack"] = "[C]: ?\n[C]: ?\n...ddOns\\AtlasLoot\\Libs\\AceLocale-3.0\\AceLocale-3.0.lua:133: in function `GetLocale'\nInterface\\AddOns\\Lorti UI\\LortiUI_Options.lua:5: in main chunk\n",
		}, -- [72]
		{
			["message"] = "...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:5: Usage: GetLocale(application[, silent]): 'application' - No locales registered for 'LortiUI'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=52c)\n",
			["timestamp"] = "2025-07-15 17:52:27",
			["context"] = "Global",
			["stack"] = "[C]: ?\n[C]: ?\n...ddOns\\AtlasLoot\\Libs\\AceLocale-3.0\\AceLocale-3.0.lua:133: in function `GetLocale'\n...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:5: in main chunk\n",
		}, -- [73]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_MediaOptions.lua:5: Usage: GetLocale(application[, silent]): 'application' - No locales registered for 'LortiUI'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=52c)\n",
			["timestamp"] = "2025-07-15 17:52:27",
			["context"] = "Global",
			["stack"] = "[C]: ?\n[C]: ?\n...ddOns\\AtlasLoot\\Libs\\AceLocale-3.0\\AceLocale-3.0.lua:133: in function `GetLocale'\nInterface\\AddOns\\Lorti UI\\LortiUI_MediaOptions.lua:5: in main chunk\n",
		}, -- [74]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_MinimapOptions.lua:5: Usage: GetLocale(application[, silent]): 'application' - No locales registered for 'LortiUI'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=52c)\n",
			["timestamp"] = "2025-07-15 17:52:27",
			["context"] = "Global",
			["stack"] = "[C]: ?\n[C]: ?\n...ddOns\\AtlasLoot\\Libs\\AceLocale-3.0\\AceLocale-3.0.lua:133: in function `GetLocale'\nInterface\\AddOns\\Lorti UI\\LortiUI_MinimapOptions.lua:5: in main chunk\n",
		}, -- [75]
		{
			["message"] = "...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1831: AceConfigRegistry-3.0:ValidateOptionsTable(): LortiUI.args.minimap.type: expected a string, got a nil",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 17:52:48",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: ?\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:50: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:45>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:226: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:219>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:254: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:219>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:282: in function `ValidateOptionsTable'\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:299: in function `app'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1831: in function `Open'\n...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1842: in function <...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1840>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:20: in function <...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:19>\n[C]: in function `Show'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:28: in function `InterfaceOptionsList_DisplayPanel'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:46: in function `InterfaceOptionsListButton_OnClick'\n[string \"*:OnClick\"]:2: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [76]
		{
			["message"] = "...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1831: AceConfigRegistry-3.0:ValidateOptionsTable(): LortiUI.args.minimap.args.enableMinimapEnhancements.name: expected a string or funcref, got 'nil'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 17:57:16",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: ?\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:50: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:45>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:210: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:205>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:244: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:219>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:254: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:219>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:254: in function <...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:219>\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:282: in function `ValidateOptionsTable'\n...-3.0\\AceConfigRegistry-3.0\\AceConfigRegistry-3.0.lua:299: in function `app'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1831: in function `Open'\n...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1842: in function <...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1840>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:20: in function <...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:19>\n[C]: in function `Show'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:28: in function `InterfaceOptionsList_DisplayPanel'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:46: in function `InterfaceOptionsListButton_OnClick'\n[string \"*:OnClick\"]:2: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [77]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Helpers.lua:10: attempt to index global 'LortiUI' (a nil value)",
			["count"] = 5,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 17:58:38",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\nInterface\\AddOns\\Lorti UI\\LortiUI_Helpers.lua:10: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1165: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1541: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1521>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...Libs\\AceGUI-3.0\\widgets\\AceGUIContainer-TabGroup.lua:156: in function `SelectTab'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1652: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1842: in function <...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1840>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:20: in function <...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:19>\n[C]: in function `Show'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:28: in function `InterfaceOptionsList_DisplayPanel'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:46: in function `InterfaceOptionsListButton_OnClick'\n[string \"*:OnClick\"]:2: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [78]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Helpers.lua:10: attempt to index global 'LortiUI' (a nil value)",
			["count"] = 5,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 18:00:23",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\nInterface\\AddOns\\Lorti UI\\LortiUI_Helpers.lua:10: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1165: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1541: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1521>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...Libs\\AceGUI-3.0\\widgets\\AceGUIContainer-TabGroup.lua:156: in function `SelectTab'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1652: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1842: in function <...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1840>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:20: in function <...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:19>\n[C]: in function `Show'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:28: in function `InterfaceOptionsList_DisplayPanel'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:46: in function `InterfaceOptionsListButton_OnClick'\n[string \"*:OnClick\"]:2: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [79]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Options.lua:35: attempt to index global 'L' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=52c)\n",
			["timestamp"] = "2025-07-15 18:02:16",
			["context"] = "Global",
			["stack"] = "(tail call): ?\nInterface\\AddOns\\Lorti UI\\LortiUI_Options.lua:35: in function `CreateFrameColorsTab'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:311: in function `SetupOptions'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:241: in function <Interface\\AddOns\\Lorti UI\\Lorti UI.lua:236>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:9: in function <[string \"safecall Dispatcher[1]\"]:5>\n(tail call): ?\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:529: in function `InitializeAddon'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:644: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:636>\n",
		}, -- [80]
		{
			["message"] = "...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:26: attempt to index global 'L' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=52c)\n",
			["timestamp"] = "2025-07-15 18:03:23",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:26: in function `CreateActionBarsTab'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:312: in function `SetupOptions'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:241: in function <Interface\\AddOns\\Lorti UI\\Lorti UI.lua:236>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:9: in function <[string \"safecall Dispatcher[1]\"]:5>\n(tail call): ?\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:529: in function `InitializeAddon'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:644: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:636>\n",
		}, -- [81]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_MediaOptions.lua:26: attempt to index global 'L' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=52c)\n",
			["timestamp"] = "2025-07-15 18:04:03",
			["context"] = "Global",
			["stack"] = "(tail call): ?\nInterface\\AddOns\\Lorti UI\\LortiUI_MediaOptions.lua:26: in function `CreateMediaEffectsTab'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:313: in function `SetupOptions'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:241: in function <Interface\\AddOns\\Lorti UI\\Lorti UI.lua:236>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:9: in function <[string \"safecall Dispatcher[1]\"]:5>\n(tail call): ?\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:529: in function `InitializeAddon'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:644: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:636>\n",
		}, -- [82]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Helpers.lua:10: attempt to index global 'LortiUI' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 18:04:56",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\nInterface\\AddOns\\Lorti UI\\LortiUI_Helpers.lua:10: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1165: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1541: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1521>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...Libs\\AceGUI-3.0\\widgets\\AceGUIContainer-TabGroup.lua:156: in function `SelectTab'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1652: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1842: in function <...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1840>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:20: in function <...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:19>\n[C]: in function `Show'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:28: in function `InterfaceOptionsList_DisplayPanel'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:46: in function `InterfaceOptionsListButton_OnClick'\n[string \"*:OnClick\"]:2: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [83]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Helpers.lua:10: attempt to index global 'LortiUI' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 18:05:33",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\nInterface\\AddOns\\Lorti UI\\LortiUI_Helpers.lua:10: in function `member'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:249: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:197>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1165: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1075>\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1616: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1541: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1521>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...Libs\\AceGUI-3.0\\widgets\\AceGUIContainer-TabGroup.lua:156: in function `SelectTab'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1652: in function `FeedGroup'\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1896: in function `Open'\n...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1842: in function <...nfig-3.0\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:1840>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:20: in function <...GUI-3.0\\widgets\\AceGUIContainer-BlizOptionsGroup.lua:19>\n[C]: in function `Show'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:28: in function `InterfaceOptionsList_DisplayPanel'\nInterface\\FrameXML\\InterfaceOptionsFrame.lua:46: in function `InterfaceOptionsListButton_OnClick'\n[string \"*:OnClick\"]:2: in function <[string \"*:OnClick\"]:1>\n",
		}, -- [84]
		{
			["message"] = "...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: LibBabble-Zone-3.0: Translation \"GetObjectType\" not found.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 18:45:07",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:25: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:23>\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:28>\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:337: in function `StyleUniversalButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:227: in function `StyleAllActionButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:408: in function `InitializeActionBarStyling'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:517: in function `UpdateActionBarStyling'\n...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:18: in function <...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:15>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:68: in function <...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:57>\n",
		}, -- [85]
		{
			["message"] = "...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: LibBabble-Zone-3.0: Translation \"GetObjectType\" not found.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 18:46:02",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:25: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:23>\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:28>\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:337: in function `StyleUniversalButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:227: in function `StyleAllActionButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:408: in function `InitializeActionBarStyling'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:517: in function `UpdateActionBarStyling'\n...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:18: in function <...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:15>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:68: in function <...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:57>\n",
		}, -- [86]
		{
			["message"] = "...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: LibBabble-Zone-3.0: Translation \"GetObjectType\" not found.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 18:54:00",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:25: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:23>\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:28>\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:338: in function `StyleUniversalButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:228: in function `StyleAllActionButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:409: in function `InitializeActionBarStyling'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:518: in function `UpdateActionBarStyling'\n...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:18: in function <...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:15>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:68: in function <...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:57>\n",
		}, -- [87]
		{
			["message"] = "...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: LibBabble-Zone-3.0: Translation \"GetObjectType\" not found.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 19:05:06",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:25: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:23>\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:28>\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:337: in function `StyleUniversalButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:227: in function `StyleAllActionButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:408: in function `InitializeActionBarStyling'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:493: in function `UpdateActionBarStyling'\n...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:17: in function <...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:14>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:68: in function <...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:57>\n",
		}, -- [88]
		{
			["message"] = "Interface\\AddOns\\PlateBuffs\\frames.lua:95: attempt to perform arithmetic on local 'size' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-15 23:34:02",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\nInterface\\AddOns\\PlateBuffs\\frames.lua:95: in function <Interface\\AddOns\\PlateBuffs\\frames.lua:90>\nInterface\\AddOns\\PlateBuffs\\frames.lua:268: in function <Interface\\AddOns\\PlateBuffs\\frames.lua:260>\n",
		}, -- [89]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\Lorti UI.lua:318: attempt to call method 'BlockDebuffColoring' (a nil value)",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-16 00:03:19",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:318: in function <Interface\\AddOns\\Lorti UI\\Lorti UI.lua:309>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[1]\"]:9: in function <[string \"safecall Dispatcher[1]\"]:5>\n(tail call): ?\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:558: in function `EnableAddon'\n...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:651: in function <...Ons\\DBM-Core\\Libs\\Ace3\\AceAddon-3.0\\AceAddon-3.0.lua:636>\n[C]: in function `LoadAddOn'\nInterface\\FrameXML\\UIParent.lua:235: in function `UIParentLoadAddOn'\nInterface\\FrameXML\\UIParent.lua:258: in function `CombatLog_LoadUI'\nInterface\\FrameXML\\UIParent.lua:482: in function <Interface\\FrameXML\\UIParent.lua:454>\n",
		}, -- [90]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: <unnamed>:SetParent(): Cannot set a 'nil' parent for fonts or textures",
			["count"] = 2,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-16 00:16:21",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: in function `SetParent'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: in function `RemoveGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:127: in function `AddGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:102: in function `EnhanceExistingFrames'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:388: in function `ApplyAllMediaEffects'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:379: in function `InitializeMediaEnhancements'\nInterface\\AddOns\\Lorti UI\\LortiUI_MediaOptions.lua:18: in function <Interface\\AddOns\\Lorti UI\\LortiUI_MediaOptions.lua:15>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:68: in function <...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:57>\n",
		}, -- [91]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: <unnamed>:SetParent(): Cannot set a 'nil' parent for fonts or textures",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-16 00:16:58",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: in function `SetParent'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: in function `RemoveGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:127: in function `AddGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:102: in function `EnhanceExistingFrames'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:388: in function `ApplyAllMediaEffects'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:379: in function `InitializeMediaEnhancements'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:385: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [92]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: <unnamed>:SetParent(): Cannot set a 'nil' parent for fonts or textures",
			["count"] = 216,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-16 02:17:47",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: in function `SetParent'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: in function `RemoveGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:127: in function `AddGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:102: in function `EnhanceExistingFrames'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:388: in function `ApplyAllMediaEffects'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:379: in function `InitializeMediaEnhancements'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:385: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [93]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: <unnamed>:SetParent(): Cannot set a 'nil' parent for fonts or textures",
			["count"] = 125,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-16 02:20:02",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n(tail call): ?\n[C]: in function `SetParent'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: in function `RemoveGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:127: in function `AddGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:102: in function `EnhanceExistingFrames'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:388: in function `ApplyAllMediaEffects'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:379: in function `InitializeMediaEnhancements'\nInterface\\AddOns\\Lorti UI\\LortiUI_MediaOptions.lua:18: in function <Interface\\AddOns\\Lorti UI\\LortiUI_MediaOptions.lua:15>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:68: in function <...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:57>\n",
		}, -- [94]
		{
			["message"] = "WTF\\Account\\GARIILA\\SavedVariables\\QDKP_V2.lua:46139: unexpected symbol near '<eof>'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=63c)\n",
			["timestamp"] = "2025-07-16 02:20:51",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [95]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: <unnamed>:SetParent(): Cannot set a 'nil' parent for fonts or textures",
			["count"] = 589,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-16 02:20:59",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: in function `SetParent'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: in function `RemoveGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:127: in function `AddGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:102: in function `EnhanceExistingFrames'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:388: in function `ApplyAllMediaEffects'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:379: in function `InitializeMediaEnhancements'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:385: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [96]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: <unnamed>:SetParent(): Cannot set a 'nil' parent for fonts or textures",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-16 02:22:30",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: in function `SetParent'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: in function `RemoveGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:127: in function `AddGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:102: in function `EnhanceExistingFrames'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:388: in function `ApplyAllMediaEffects'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:379: in function `InitializeMediaEnhancements'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:385: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [97]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: <unnamed>:SetParent(): Cannot set a 'nil' parent for fonts or textures",
			["count"] = 90,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=9cb)\n",
			["timestamp"] = "2025-07-16 02:24:16",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n[C]: ?\n[C]: in function `SetParent'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:150: in function `RemoveGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:127: in function `AddGlowEffect'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:102: in function `EnhanceExistingFrames'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:388: in function `ApplyAllMediaEffects'\nInterface\\AddOns\\Lorti UI\\LortiUI_Media.lua:379: in function `InitializeMediaEnhancements'\nInterface\\AddOns\\Lorti UI\\Lorti UI.lua:385: in function `Callback'\nInterface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:16: in function <Interface\\AddOns\\!!!ClassicAPI\\Util\\C_Timer.lua:15>\n",
		}, -- [98]
		{
			["message"] = "Interface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:316: '<eof>' expected near 'end'",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  Scrap, v\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=52c)\n",
			["timestamp"] = "2025-07-16 03:40:23",
			["context"] = "Global",
			["stack"] = "[C]: ?\n",
		}, -- [99]
		{
			["message"] = "...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: LibBabble-Zone-3.0: Translation \"GetObjectType\" not found.",
			["count"] = 1,
			["addons"] = "  ClassicAPI, v1.1.93\n  BlizzBugsSuck, v3.3.5.9\n  KRT, v0.5.6b\n  Swatter, v3.1.14 (<%codename%>)\n  NPCScan, v3.3.5.5\n  NPCScanOverlay, v3.3.5.1\n  ACP, v3.3.5 \n  Align, v\n  Atlas, v1.16.1\n  AtlasBattlegrounds, v1.16.1\n  AtlasDungeonLocs, v1.16.1\n  AtlasOutdoorRaids, v1.16.1\n  AtlasTransportation, v1.16.1\n  AtlasLoot, vv5.11.04\n  AtlasLootFu, vv5.11.04\n  AtlasQuest, v4.4.3\n  Auctionator, v2.6.8\n  Babylonian, v5.1.DEV.130\n  BagnonForever, v1.1.2\n  BagnonTooltips, v\n  BASpammer, v1.0\n  Binder, v2.1.0\n  Bison, v1.4.3\n  BonusScanner, v5.3\n  Chatter, v1.0\n  ClassIconNotPortrait, v\n  Collectinator, v1.0.4\n  Combat, v1.0\n  CombatLogFix, v\n  Combuctor, v2.2.2\n  CombuctorConfig, v\n  CombuctorCustomSets, v0.4\n  CombuctorScrap, v1.4\n  CombuctorSets, v\n  Configator, v5.1.DEV.190\n  Cromulent, vv1.5.2\n  DBMCore, v10.1.13_alpha\n  DBMSpellTimers, v\n  DBMStatusBarTimers, v10.1.13_alpha\n  DBMVPVEM, v49d3d7e\n  DiminishingReturns, vv1.0-beta-12\n  DiminishingReturnsConfig, vv1.0-beta-12\n  Dominos, v1.18.6\n  DominosCast, v\n  DominosConfig, v\n  DominosRoll, v\n  DominosTotems, v\n  DominosXP, v\n  DrDamage, v1.7.8_release\n  DressMe, v1.3.1\n  Elephant, v2.9.6-beta\n  Flightmap, v\n  Gatherer, v3.1.14\n  GathererDBWowhead, v1.0.2009-12-09\n  GearScore, v3.1.17 - Release\n  Gladius, vv1.2.0\n  HideFrame, v2.0\n  Icicle, v0.9\n  LibExtraTip, v5.12.DEV.350(/embedded)\n  LootLog, v1.6.0\n  LortiUI, v3.0.0\n  LoseControl, v3.33\n  Mapster, v1.3.9\n  MaxCamEnhanced, v2.0\n  MouseSpeedEnhanced, v2.0\n  MoveAnything, v11.b1\n  NugComboBar, v\n  Omen, v3.0.9\n  OmniCC, v3.0.2\n  OmniCCConfig, v\n  PartyTargets, v3.3-2\n  PlateBuffs, v1.18.2 (r230)\n  Postal, v3.4.0\n  QDKP2GUI, v2.6.7.13\n  QDKPV2, v2.6.7.13\n  Quartz, v3.0.3.1\n  Questie335, v9.5.1\n  RaidAchievement, v1.036\n  RaidBrowser, vv1.3.0\n  RaidRoll, v\n  RaidRollLootTracker, v\n  RangeColors, v\n  SavedInstances, vv3.0.1 (r119)\n  Scrap, v\n  SexyMap, v\n  Sifter, vr19\n  SilverDragon, vv2.3.4-5-g99c69f3\n  Skada, v1.8.87\n  SkadaImprovement, v\n  SkadaStorage, v\n  SnowfallKeyPress, v\n  SoundAlerter, v1.0\n  Spy, v\n  TargetHealthPercent, v5.3\n  TellMeWhen, v1.5.4\n  TidyPlates, v6.5.0\n  TidyPlatesThreatPlates, v5.7\n  TomTom, v237\n  TradeSkillMaster, vv2.8.3\n  TradeSkillMasterAccounting, vv2.3\n  TradeSkillMasterAuctionDB, vv2.3.10\n  TradeSkillMasterAuctioning, vv2.3.3\n  TradeSkillMasterCrafting, vv2.5.2\n  TradeSkillMasterDestroying, vv2.1.1\n  TradeSkillMasterItemTracker, vv2.0.8\n  TradeSkillMasterShopping, vv2.3.6\n  TradeSkillMasterWarehousing, vv2.0.10\n  UnitFramesImproved, v1.4.4\n  BlizRuntimeLib_ruRU v3.3.5.30300 <logon.warmane.com>\n  (ck=a05)\n",
			["timestamp"] = "2025-07-16 05:01:25",
			["context"] = "Global",
			["stack"] = "(tail call): ?\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:25: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:23>\n...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:51: in function <...\\AtlasLoot\\Libs\\LibBabble-Boss-3.0\\LibBabble-3.0.lua:28>\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:337: in function `StyleUniversalButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:227: in function `StyleAllActionButtons'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:408: in function `InitializeActionBarStyling'\nInterface\\AddOns\\Lorti UI\\LortiUI_ActionBars.lua:493: in function `UpdateActionBarStyling'\n...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:18: in function <...terface\\AddOns\\Lorti UI\\LortiUI_ActionBarOptions.lua:15>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[2]\"]:9: in function <[string \"safecall Dispatcher[2]\"]:5>\n(tail call): ?\n...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:818: in function <...pat\\Libs\\AceConfigDialog-3.0\\AceConfigDialog-3.0.lua:639>\n(tail call): ?\n[C]: ?\n[string \"safecall Dispatcher[3]\"]:9: in function <[string \"safecall Dispatcher[3]\"]:5>\n(tail call): ?\n...dOns\\TradeSkillMaster\\Libs\\AceGUI-3.0\\AceGUI-3.0.lua:334: in function `Fire'\n...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:68: in function <...er\\Libs\\AceGUI-3.0\\widgets\\AceGUIWidget-CheckBox.lua:57>\n",
		}, -- [100]
	},
}
