-- Lorti UI: Configurable frame colors addon for WoW 3.3.5a
-- Changes unit frame textures to customizable colors with Ace3 interface

local LortiUI = LibStub("AceAddon-3.0"):NewAddon("LortiUI", "AceConsole-3.0")

-- Get localization table
local L = LortiUI_Locale

-- Simplified default configuration
local defaults = {
    profile = {
        -- Frame colors
        enablePlayerColor = false,
        enableTargetColor = false,
        enableFocusColor = false,
        enablePartyColor = false,
        enableBossColor = false,
        enableArenaColor = false,
        enablePetColor = false,
        enableVehicleColor = false,

        playerColor = {r = 0, g = 0, b = 0},
        targetColor = {r = 0, g = 0, b = 0},
        focusColor = {r = 0, g = 0, b = 0},
        partyColor = {r = 0, g = 0, b = 0},
        bossColor = {r = 0, g = 0, b = 0},
        arenaColor = {r = 0, g = 0, b = 0},
        petColor = {r = 0, g = 0, b = 0},
        vehicleColor = {r = 0, g = 0, b = 0},

        -- Action bars
        enableButtonStyling = true,
        enableButtonBackground = true,
        enableButtonShadow = true,
        useFlatButtonBackground = false,
        forceButtonStyling = false,
        enableUniversalStyling = false,
        styleStandardBars = true,
        styleDominosBars = true,
        styleBartenderBars = true,
        styleOtherAddonBars = true,
        buttonBackgroundColor = {r = 0.2, g = 0.2, b = 0.2, a = 0.3},
        buttonShadowColor = {r = 0, g = 0, b = 0, a = 0.9},
        buttonNormalColor = {r = 0.37, g = 0.3, b = 0.3},

        -- Media effects
        enableGlowEffects = true,
        enablePlayerGlow = true,
        enableTargetGlow = true,
        glowIntensity = 0.5,
        enableTransparencyEffects = true,
        frameTransparency = 0.8,
        enableAnimations = true,
        animationSpeed = 1.0,
        glowSize = 5,

        -- Minimap
        enableMinimapEnhancements = false,
        minimapScale = 1.0,
        enableMinimapBorder = false,
        enableMinimapBackground = false,
        minimapTransparency = 1.0,
        enableMinimapMove = false,
    }
}

-- Centralized frame data (simple approach)
local FRAME_DATA = {
    player = {
        textures = {"PlayerFrameTexture"},
        enableKey = "enablePlayerColor",
        colorKey = "playerColor"
    },
    target = {
        textures = function()
            local textures = {}
            -- Используем безопасные текстуры для target фрейма
            if _G["TargetFrameBackground"] then
                table.insert(textures, "TargetFrameBackground")
            end
            if _G["TargetFrameToTTextureFrameTexture"] then
                table.insert(textures, "TargetFrameToTTextureFrameTexture")
            end
            -- Добавляем основную текстуру, но с осторожностью
            if _G["TargetFrameTextureFrameTexture"] then
                table.insert(textures, "TargetFrameTextureFrameTexture")
            end
            return textures
        end,
        enableKey = "enableTargetColor",
        colorKey = "targetColor"
    },
    focus = {
        textures = function()
            local textures = {}
            -- Используем безопасные текстуры для focus фрейма
            if _G["FocusFrameBackground"] then
                table.insert(textures, "FocusFrameBackground")
            end
            if _G["FocusFrameToTTextureFrameTexture"] then
                table.insert(textures, "FocusFrameToTTextureFrameTexture")
            end
            -- Добавляем основную текстуру, но с осторожностью
            if _G["FocusFrameTextureFrameTexture"] then
                table.insert(textures, "FocusFrameTextureFrameTexture")
            end
            return textures
        end,
        enableKey = "enableFocusColor",
        colorKey = "focusColor"
    },
    party = {
        textures = function()
            local textures = {}
            for i = 1, 4 do
                table.insert(textures, "PartyMemberFrame"..i.."Texture")
            end
            return textures
        end,
        enableKey = "enablePartyColor",
        colorKey = "partyColor"
    },
    pet = {
        textures = {"PetFrameTexture"},
        enableKey = "enablePetColor",
        colorKey = "petColor"
    },
    vehicle = {
        textures = {"VehicleMenuBarTexture", "PlayerVehicleTexture"},
        enableKey = "enableVehicleColor",
        colorKey = "vehicleColor",
        condition = function() return UnitHasVehicleUI("player") end
    },
    boss = {
        textures = function()
            local textures = {}
            for i = 1, 4 do
                table.insert(textures, "Boss"..i.."TargetFrameTexture")
            end
            return textures
        end,
        enableKey = "enableBossColor",
        colorKey = "bossColor"
    },
    arena = {
        textures = function()
            local textures = {}
            for i = 1, 5 do
                table.insert(textures, "ArenaEnemyFrame"..i.."Texture")
            end
            return textures
        end,
        enableKey = "enableArenaColor",
        colorKey = "arenaColor"
    }
}

-- Simple color application function with protection against resets
local function ApplyFrameColor(frameType, db)
    local frameData = FRAME_DATA[frameType]
    if not frameData then return end

    local textures = type(frameData.textures) == "function" and frameData.textures() or frameData.textures
    if not textures then return end

    if db[frameData.enableKey] then
        local color = db[frameData.colorKey]
        if not color then return end

        -- Apply color and protect against resets
        for _, textureName in ipairs(textures) do
            local texture = _G[textureName]
            if texture and texture.SetVertexColor then
                -- Store our color
                texture.lortiColor = {r = color.r or 0, g = color.g or 0, b = color.b or 0}

                -- Apply color (NO HOOKS)
                texture:SetVertexColor(color.r or 0, color.g or 0, color.b or 0)
            end
        end
    else
        -- Reset to white when disabled (NO HOOKS TO REMOVE)
        for _, textureName in ipairs(textures) do
            local texture = _G[textureName]
            if texture and texture.SetVertexColor then
                texture:SetVertexColor(1, 1, 1)
            end
        end
    end
end

-- Simplified apply function
function LortiUI:ApplyFrameColors(frameType)
    ApplyFrameColor(frameType, self.db.profile)
end

-- Function to refresh and reapply colors
function LortiUI:RefreshFrameColors()
    self:ApplyAllColors()
end

-- Legacy functions (now handled by centralized system)
function LortiUI:ApplyBossFrameColors()
    self:ApplyFrameColors("boss")
end

function LortiUI:ApplyArenaFrameColors()
    self:ApplyFrameColors("arena")
end

-- Apply all frame colors and media effects (rewritten for stability)
function LortiUI:ApplyAllColors()
    local db = self.db.profile

    -- Apply all frame colors first
    for frameType, _ in pairs(FRAME_DATA) do
        ApplyFrameColor(frameType, db)
    end

    -- Apply media effects using new safe functions
    if self.ApplyAllMediaEffects then
        self:ApplyAllMediaEffects()
    end

    -- Add flash effect to indicate colors were applied
    if self.FlashEffect and PlayerFrame then
        self:FlashEffect(PlayerFrame)
    end
end

-- Function to reset all colors to default black
function LortiUI:ResetToDefault()
    local db = self.db.profile

    db.playerColor = {r = 0, g = 0, b = 0}
    db.targetColor = {r = 0, g = 0, b = 0}
    db.focusColor = {r = 0, g = 0, b = 0}
    db.partyColor = {r = 0, g = 0, b = 0}
    db.bossColor = {r = 0, g = 0, b = 0}
    db.arenaColor = {r = 0, g = 0, b = 0}
    db.petColor = {r = 0, g = 0, b = 0}
    db.vehicleColor = {r = 0, g = 0, b = 0}

    self:ApplyAllColors()
    self:Print(L["All colors reset to default black"] or "All colors reset to default black")
    if self.ShowStatus then
        self:ShowStatus(L["Colors reset to default!"] or "Colors reset to default!", 2)
    end
end

-- Handle arena frames loading
function LortiUI:HandleArenaFrames()
    local arenaFrame = CreateFrame("Frame")
    arenaFrame:RegisterEvent("ADDON_LOADED")
    arenaFrame:SetScript("OnEvent", function(self, event, addon)
        if addon == "Blizzard_ArenaUI" then
            LortiUI:ApplyArenaFrameColors()
            self:UnregisterEvent("ADDON_LOADED")
        end
    end)
end

-- Handle boss frames loading
function LortiUI:HandleBossFrames()
    local bossFrame = CreateFrame("Frame")
    bossFrame:RegisterEvent("ADDON_LOADED")
    bossFrame:SetScript("OnEvent", function(self, event, addon)
        if addon == "Blizzard_BossFrames" then
            LortiUI:ApplyBossFrameColors()
            self:UnregisterEvent("ADDON_LOADED")
        end
    end)
end

-- Block WoW's automatic debuff coloring that causes frames to change colors
function LortiUI:BlockDebuffColoring()
    -- Hook the functions that change frame colors based on debuff types
    if UnitFrame_Update then
        local originalUnitFrame_Update = UnitFrame_Update
        UnitFrame_Update = function(frame, ...)
            originalUnitFrame_Update(frame, ...)
            -- Reapply our colors after WoW tries to change them
            if frame == TargetFrame then
                self:ApplyFrameColors("target")
            elseif frame == FocusFrame then
                self:ApplyFrameColors("focus")
            elseif frame == PlayerFrame then
                self:ApplyFrameColors("player")
            end
        end
    end

    -- Hook TargetFrame_CheckFaction (this changes colors based on faction/debuffs)
    if TargetFrame_CheckFaction then
        local originalTargetFrame_CheckFaction = TargetFrame_CheckFaction
        TargetFrame_CheckFaction = function(...)
            originalTargetFrame_CheckFaction(...)
            -- Reapply our target colors after faction check
            self:ApplyFrameColors("target")
        end
    end

    -- Hook TargetFrame_CheckClassification (this changes colors based on elite/rare status)
    if TargetFrame_CheckClassification then
        local originalTargetFrame_CheckClassification = TargetFrame_CheckClassification
        TargetFrame_CheckClassification = function(...)
            originalTargetFrame_CheckClassification(...)
            -- Reapply our target colors after classification check
            self:ApplyFrameColors("target")
        end
    end

    -- Hook FocusFrame_CheckFaction
    if FocusFrame_CheckFaction then
        local originalFocusFrame_CheckFaction = FocusFrame_CheckFaction
        FocusFrame_CheckFaction = function(...)
            originalFocusFrame_CheckFaction(...)
            -- Reapply our focus colors after faction check
            self:ApplyFrameColors("focus")
        end
    end

    -- Hook the debuff border coloring function
    if TargetFrame_UpdateAuras then
        local originalTargetFrame_UpdateAuras = TargetFrame_UpdateAuras
        TargetFrame_UpdateAuras = function(...)
            originalTargetFrame_UpdateAuras(...)
            -- Reapply our target colors after aura update
            self:ApplyFrameColors("target")
        end
    end

    if FocusFrame_UpdateAuras then
        local originalFocusFrame_UpdateAuras = FocusFrame_UpdateAuras
        FocusFrame_UpdateAuras = function(...)
            originalFocusFrame_UpdateAuras(...)
            -- Reapply our focus colors after aura update
            self:ApplyFrameColors("focus")
        end
    end
end

-- Addon initialization
function LortiUI:OnInitialize()
    -- Initialize database
    self.db = LibStub("AceDB-3.0"):New("LortiUIDB", defaults, true)

    -- Setup options
    self:SetupOptions()

    -- Register slash command
    self:RegisterChatCommand("lortiui", "SlashCommand")
    self:RegisterChatCommand("lui", "SlashCommand")
end

function LortiUI:OnEnable()
    -- Apply colors when addon enables
    self:ApplyAllColors()

    -- Setup special frame handlers
    self:HandleArenaFrames()
    self:HandleBossFrames()

    -- Block WoW's automatic debuff coloring (this is what causes frames to change during combat/debuffs)
    self:BlockDebuffColoring()

    -- Initialize media enhancements
    C_Timer.After(1, function()
        if self.InitializeMediaEnhancements then
            self:InitializeMediaEnhancements()
        end
    end)

    -- Initialize action bar styling (only if enabled) - FIXED TIMER
    C_Timer.After(2, function()
        if self.InitializeActionBarStyling then
            -- Check current setting, not the setting from 2 seconds ago!
            if self.db and self.db.profile and self.db.profile.enableButtonStyling then
                self:InitializeActionBarStyling()
            end
        end
    end)

    -- Initialize minimap enhancements
    C_Timer.After(0.5, function()
        if self.InitializeMinimapEnhancements then
            self:InitializeMinimapEnhancements()
        end
    end)

    self:Print(L["Lorti UI loaded. Use /lortiui or /lui to open settings."] or "Lorti UI loaded. Use /lortiui or /lui to open settings.")
end



-- Slash command handler
function LortiUI:SlashCommand(input)
    if not input or input:trim() == "" then
        -- Open config dialog
        LibStub("AceConfigDialog-3.0"):Open("LortiUI")
    elseif input:lower() == "apply" then
        self:ApplyAllColors()
        self:Print(L["Colors applied to all frames"] or "Colors applied to all frames")
        if self.ShowStatus then
            self:ShowStatus(L["Colors applied!"] or "Colors applied!", 2)
        end
    elseif input:lower() == "reset" then
        self:ResetToDefault()
    else
        self:Print(L["Usage: /lortiui - open settings, /lortiui apply - apply colors, /lortiui reset - reset to default"] or "Usage: /lortiui - open settings, /lortiui apply - apply colors, /lortiui reset - reset to default")
    end
end



-- Simplified setup - helper functions moved to option files

-- Setup options interface with tabs (like professional addons)
function LortiUI:SetupOptions()
    local options = {
        type = "group",
        name = L["Lorti UI"] or "Lorti UI",
        childGroups = "tab",
        args = {
            frameColors = LortiUI.CreateFrameColorsTab and LortiUI.CreateFrameColorsTab() or {},
            actionBars = LortiUI.CreateActionBarsTab and LortiUI.CreateActionBarsTab() or {},
            mediaEffects = LortiUI.CreateMediaEffectsTab and LortiUI.CreateMediaEffectsTab() or {},
            minimap = LortiUI.CreateMinimapTab and LortiUI.CreateMinimapTab() or {},
        }
    }

    -- Register options
    LibStub("AceConfig-3.0"):RegisterOptionsTable("LortiUI", options)
    LibStub("AceConfigDialog-3.0"):AddToBlizOptions("LortiUI", "Lorti UI")
end