-- Lorti UI Media Integration
-- Enhances UI elements using media resources

local LortiUI = LibStub("AceAddon-3.0"):GetAddon("LortiUI")

-- Media paths
local MEDIA_PATH = "Interface\\AddOns\\Lorti UI\\media\\"
local MEDIA_TEXTURES = {
    buttonBackground = MEDIA_PATH .. "button_background",
    buttonBackgroundFlat = MEDIA_PATH .. "button_background_flat",
    checked = MEDIA_PATH .. "checked",
    flash = MEDIA_PATH .. "flash",
    gloss = MEDIA_PATH .. "gloss",
    glossGrey = MEDIA_PATH .. "gloss_grey",
    hover = MEDIA_PATH .. "hover",
    outerShadow = MEDIA_PATH .. "outer_shadow",
    pushed = MEDIA_PATH .. "pushed",
}

-- Function to create enhanced button with media textures
function LortiUI:CreateEnhancedButton(parent, text, width, height)
    local button = Create<PERSON>rame("But<PERSON>", nil, parent)
    button:SetSize(width or 120, height or 25)
    
    -- Set button textures using media resources
    button:SetNormalTexture(MEDIA_TEXTURES.buttonBackground)
    button:SetHighlightTexture(MEDIA_TEXTURES.hover)
    button:SetPushedTexture(MEDIA_TEXTURES.pushed)
    
    -- Add gloss overlay
    local gloss = button:CreateTexture(nil, "OVERLAY")
    gloss:SetTexture(MEDIA_TEXTURES.gloss)
    gloss:SetAllPoints(button)
    gloss:SetAlpha(0.3)
    -- Make background transparent to avoid square artifacts
    gloss:SetTexCoord(0.1, 0.9, 0.1, 0.9)
    
    -- Add text
    local fontString = button:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    fontString:SetPoint("CENTER")
    fontString:SetText(text or "Button")
    button.text = fontString
    
    return button
end

-- Function to create enhanced checkbox with media textures
function LortiUI:CreateEnhancedCheckbox(parent, text)
    local checkbox = CreateFrame("CheckButton", nil, parent, "UICheckButtonTemplate")
    checkbox:SetSize(24, 24)

    -- Set custom textures
    checkbox:SetNormalTexture(MEDIA_TEXTURES.buttonBackgroundFlat)
    checkbox:SetCheckedTexture(MEDIA_TEXTURES.checked)
    checkbox:SetHighlightTexture(MEDIA_TEXTURES.hover)

    -- Add text label
    if text then
        local label = checkbox:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        label:SetPoint("LEFT", checkbox, "RIGHT", 5, 0)
        label:SetText(text)
        checkbox.label = label
    end

    return checkbox
end

-- Function to create enhanced frame with media styling
function LortiUI:CreateEnhancedFrame(parent, width, height)
    local frame = CreateFrame("Frame", nil, parent)
    frame:SetSize(width or 200, height or 100)
    
    -- Set background
    local bg = frame:CreateTexture(nil, "BACKGROUND")
    bg:SetTexture(MEDIA_TEXTURES.buttonBackgroundFlat)
    bg:SetAllPoints(frame)
    bg:SetAlpha(0.8)
    
    -- Add outer shadow
    local shadow = frame:CreateTexture(nil, "BORDER")
    shadow:SetTexture(MEDIA_TEXTURES.outerShadow)
    shadow:SetPoint("TOPLEFT", frame, "TOPLEFT", -8, 8)
    shadow:SetPoint("BOTTOMRIGHT", frame, "BOTTOMRIGHT", 8, -8)
    shadow:SetAlpha(0.5)
    
    return frame
end

-- Function to add visual effects to existing frames (completely rewritten for WoW 3.3.5a)
function LortiUI:EnhanceExistingFrames()
    -- Only enhance if enabled in settings
    if not self.db.profile.enableGlowEffects then
        -- Remove all glow effects when disabled
        self:RemoveGlowEffect(PlayerFrame)
        self:RemoveGlowEffect(TargetFrame)
        self:RemoveGlowEffect(FocusFrame)
        return
    end

    -- Add glow effect to player frame
    if PlayerFrame and self.db.profile.enablePlayerGlow then
        self:AddGlowEffect(PlayerFrame, self.db.profile.playerColor)
    else
        self:RemoveGlowEffect(PlayerFrame)
    end

    -- Add glow effect to target frame
    if TargetFrame and self.db.profile.enableTargetGlow then
        self:AddGlowEffect(TargetFrame, self.db.profile.targetColor)
    else
        self:RemoveGlowEffect(TargetFrame)
    end

    -- Add glow effect to focus frame
    if FocusFrame and self.db.profile.enableTargetGlow then -- Use same setting as target
        self:AddGlowEffect(FocusFrame, self.db.profile.focusColor)
    else
        self:RemoveGlowEffect(FocusFrame)
    end
end

-- Simple and reliable glow effect function
function LortiUI:AddGlowEffect(frame, color)
    if not frame then return end

    -- Remove existing glow first
    self:RemoveGlowEffect(frame)

    -- Create simple glow texture
    local glow = frame:CreateTexture(nil, "BACKGROUND")
    glow:SetTexture("Interface\\Buttons\\WHITE8X8") -- Simple white texture
    glow:SetAllPoints(frame)

    -- Set glow color and intensity
    local glowColor = color or {r = 1, g = 1, b = 1}
    local intensity = self.db.profile.glowIntensity or 0.5

    glow:SetVertexColor(glowColor.r, glowColor.g, glowColor.b, intensity)
    glow:SetBlendMode("ADD") -- Additive blending for glow effect
    glow:Show()

    -- Store reference for cleanup
    frame.lortiGlow = glow
end

-- Remove glow effect (fixed for WoW 3.3.5a)
function LortiUI:RemoveGlowEffect(frame)
    if frame and frame.lortiGlow then
        frame.lortiGlow:Hide()
        -- Don't use SetParent(nil) in WoW 3.3.5a - just hide and clear reference
        frame.lortiGlow = nil
    end
end

-- Function to add disabled overlay
function LortiUI:AddDisabledOverlay(frame)
    if not frame or frame.lortiDisabledOverlay then return end

    -- Create a very subtle overlay that doesn't look like a square
    local overlay = frame:CreateTexture(nil, "OVERLAY")
    -- Try custom texture first, fallback to solid color
    if MEDIA_TEXTURES.glossGrey then
        overlay:SetTexture(MEDIA_TEXTURES.glossGrey)
    else
        overlay:SetTexture("Interface\\Buttons\\WHITE8X8")
    end
    overlay:SetAllPoints(frame)
    overlay:SetAlpha(0.1) -- Much more subtle
    overlay:SetBlendMode("BLEND") -- Normal blending
    overlay:SetVertexColor(0.5, 0.5, 0.5) -- Grey tint
    frame.lortiDisabledOverlay = overlay
end

-- Function to remove disabled overlay
function LortiUI:RemoveDisabledOverlay(frame)
    if frame and frame.lortiDisabledOverlay then
        frame.lortiDisabledOverlay:Hide()
        -- Don't set parent to nil in WoW 3.3.5a, just hide and clear reference
        frame.lortiDisabledOverlay = nil
    end
end

-- Function to create color preview frame
function LortiUI:CreateColorPreview()
    local previewFrame = self:CreateEnhancedFrame(UIParent, 150, 100)
    previewFrame:SetPoint("CENTER", UIParent, "CENTER", 300, 0)
    previewFrame:Hide()
    
    -- Title
    local title = previewFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    title:SetPoint("TOP", previewFrame, "TOP", 0, -10)
    title:SetText("Color Preview")
    
    -- Preview texture
    local preview = previewFrame:CreateTexture(nil, "ARTWORK")
    preview:SetTexture(MEDIA_TEXTURES.buttonBackground)
    preview:SetSize(80, 40)
    preview:SetPoint("CENTER", previewFrame, "CENTER", 0, -10)
    -- Make background transparent to avoid square artifacts
    preview:SetTexCoord(0.1, 0.9, 0.1, 0.9)
    
    previewFrame.preview = preview
    self.colorPreviewFrame = previewFrame
    
    return previewFrame
end

-- Function to update color preview
function LortiUI:UpdateColorPreview(r, g, b)
    if not self.db.profile.enableColorPreview then return end

    if self.colorPreviewFrame and self.colorPreviewFrame.preview then
        self.colorPreviewFrame.preview:SetVertexColor(r, g, b)
        self.colorPreviewFrame:Show()

        -- Add flash effect
        self:FlashEffect(self.colorPreviewFrame)

        -- Auto-hide after 3 seconds
        C_Timer.After(3, function()
            if self.colorPreviewFrame then
                self.colorPreviewFrame:Hide()
            end
        end)
    end
end

-- Function to create flash effect
function LortiUI:FlashEffect(frame)
    if not frame then return end

    local flash = frame:CreateTexture(nil, "OVERLAY")
    flash:SetTexture(MEDIA_TEXTURES.flash)
    flash:SetAllPoints(frame)
    flash:SetAlpha(0.8)
    flash:SetVertexColor(1, 1, 1)
    -- Make background transparent to avoid square artifacts
    flash:SetTexCoord(0.1, 0.9, 0.1, 0.9)

    -- Animate flash
    local fadeOut = flash:CreateAnimationGroup()
    local alpha = fadeOut:CreateAnimation("Alpha")
    alpha:SetFromAlpha(0.8)
    alpha:SetToAlpha(0)
    alpha:SetDuration(0.5)
    alpha:SetScript("OnFinished", function()
        flash:Hide()
        -- Don't set parent to nil in WoW 3.3.5a, just hide the texture
    end)

    fadeOut:Play()
end

-- Function to create status indicator
function LortiUI:CreateStatusIndicator()
    if not self.db.profile.enableStatusIndicator then return nil end

    local indicator = self:CreateEnhancedFrame(UIParent, 200, 30)
    indicator:SetPoint("TOP", UIParent, "TOP", 0, -100)
    indicator:Hide()

    -- Status text
    local text = indicator:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    text:SetPoint("CENTER")
    text:SetTextColor(1, 1, 1)
    indicator.text = text

    self.statusIndicator = indicator
    return indicator
end

-- Function to show status message
function LortiUI:ShowStatus(message, duration)
    if not self.db.profile.enableStatusIndicator or not self.statusIndicator then return end

    self.statusIndicator.text:SetText(message)
    self.statusIndicator:Show()

    -- Auto-hide after duration
    C_Timer.After(duration or 2, function()
        if self.statusIndicator then
            self.statusIndicator:Hide()
        end
    end)
end

-- Function to apply transparency effects (completely rewritten - safe and working)
function LortiUI:ApplyTransparencyEffects()
    if not self.db.profile.enableTransparencyEffects then
        -- Reset to full opacity when disabled
        self:SetFrameTransparency(PlayerFrame, 1.0)
        self:SetFrameTransparency(TargetFrame, 1.0)
        self:SetFrameTransparency(FocusFrame, 1.0)
        return
    end

    local transparency = self.db.profile.frameTransparency or 0.8

    -- Apply transparency safely
    self:SetFrameTransparency(PlayerFrame, transparency)
    self:SetFrameTransparency(TargetFrame, transparency)
    self:SetFrameTransparency(FocusFrame, transparency)
end

-- Safe transparency function that doesn't break frames
function LortiUI:SetFrameTransparency(frame, alpha)
    if not frame then return end

    -- Store original alpha if not stored yet
    if not frame.lortiOriginalAlpha then
        frame.lortiOriginalAlpha = frame:GetAlpha()
    end

    -- Apply transparency only to the frame itself, not children
    frame:SetAlpha(alpha)

    -- Mark that we've modified this frame
    frame.lortiTransparencyApplied = true
end

-- Function to apply animation effects (completely rewritten - safe and working)
function LortiUI:ApplyAnimationEffects()
    if not self.db.profile.enableAnimations then
        -- Remove all animations when disabled
        self:RemoveFrameAnimation(PlayerFrame)
        self:RemoveFrameAnimation(TargetFrame)
        self:RemoveFrameAnimation(FocusFrame)
        return
    end

    local speed = self.db.profile.animationSpeed or 1.0

    -- Add simple pulse animation to frames
    self:AddFrameAnimation(PlayerFrame, speed)
    self:AddFrameAnimation(TargetFrame, speed)
    self:AddFrameAnimation(FocusFrame, speed)
end

-- Add simple pulse animation that doesn't break frames (FIXED MЕРЦАНИЕ)
function LortiUI:AddFrameAnimation(frame, speed)
    if not frame then return end

    -- Remove existing animation first
    self:RemoveFrameAnimation(frame)

    -- Create SUBTLE alpha animation instead of scale
    local animGroup = frame:CreateAnimationGroup()
    animGroup:SetLooping("BOUNCE")

    local alpha = animGroup:CreateAnimation("Alpha")
    alpha:SetFromAlpha(1.0)
    alpha:SetToAlpha(0.9) -- Very subtle alpha change
    alpha:SetDuration(3.0 / speed) -- Slower duration
    alpha:SetSmoothing("IN_OUT")

    -- Start animation
    animGroup:Play()

    -- Store reference for cleanup
    frame.lortiAnimation = animGroup
end

-- Remove frame animation
function LortiUI:RemoveFrameAnimation(frame)
    if frame and frame.lortiAnimation then
        frame.lortiAnimation:Stop()
        frame.lortiAnimation = nil
    end
end

-- Initialize media enhancements (simplified and safe)
function LortiUI:InitializeMediaEnhancements()
    -- Create preview frame
    self:CreateColorPreview()

    -- Create status indicator
    self:CreateStatusIndicator()

    -- Apply all media effects
    self:ApplyAllMediaEffects()

    -- Show initialization message
    self:ShowStatus("Lorti UI Media Enhanced!", 3)
end

-- Apply all media effects in correct order
function LortiUI:ApplyAllMediaEffects()
    -- Apply effects in safe order
    self:EnhanceExistingFrames()      -- Glow effects
    self:ApplyTransparencyEffects()   -- Transparency
    self:ApplyAnimationEffects()      -- Animations
end
