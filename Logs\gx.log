7/16 04:17:12.246  CGxDevice::DeviceAdapterID(): RET: 1, VID: 10de, DID: 1c82, DVER: 0.0
7/16 04:17:12.411  CGxDevice::DeviceAdapterInfer(): RET: 1, DID: 3
7/16 04:17:12.411  ConsoleDetectDetectHardware():
7/16 04:17:12.411  	cpuIdx: 0
7/16 04:17:12.411  	videoID: 693
7/16 04:17:12.411  	soundIdx: 0
7/16 04:17:12.411  	memIdx: 0
7/16 04:17:12.459  ValidateFormatMonitor(): unable to find monitor refresh
7/16 04:17:12.459  ValidateFormatMonitor(): invalid refresh rate 60, set to 60
7/16 04:17:12.459  ConsoleDeviceInitialize(): hwDetect = 0, hwChanged = 0
7/16 04:17:12.581  CGxDeviceD3d::DeviceSetFormat():
7/16 04:17:12.581  	Format: 1920 x 1080 Window, Ds24X, multisample 8
7/16 04:17:12.694  Caps:
7/16 04:17:12.694  	numTmus: 8
7/16 04:17:12.694  	generateMipMaps: 1
7/16 04:17:12.694  	texFilterAnisotropic: 1, 16
7/16 04:17:12.694  	rttFormat: 1, 1
7/16 04:17:12.694  	pixelShaderTarget: ps_3_0
7/16 04:17:12.694  	vertexShaderTarget: vs_3_0
7/16 04:17:12.694  	vertexShaderConstants: 256
7/16 04:17:12.694  	numStreams: 16
7/16 04:17:12.694  	stereoAvailable: 0
7/16 04:17:12.694  	NVAPI: 1
7/16 04:17:12.694  	stereoHandle: 0
