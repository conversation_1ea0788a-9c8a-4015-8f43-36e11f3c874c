7/16 04:26:37.918  CGxDevice::DeviceAdapterID(): RET: 1, VID: 10de, DID: 1c82, DVER: 0.0
7/16 04:26:38.037  CGxDevice::DeviceAdapterInfer(): RET: 1, DID: 3
7/16 04:26:38.037  ConsoleDetectDetectHardware():
7/16 04:26:38.037  	cpuIdx: 0
7/16 04:26:38.037  	videoID: 693
7/16 04:26:38.037  	soundIdx: 0
7/16 04:26:38.037  	memIdx: 0
7/16 04:26:38.089  ValidateFormatMonitor(): unable to find monitor refresh
7/16 04:26:38.089  ValidateFormatMonitor(): invalid refresh rate 60, set to 60
7/16 04:26:38.089  ConsoleDeviceInitialize(): hwDetect = 0, hwChanged = 0
7/16 04:26:38.197  CGxDeviceD3d::DeviceSetFormat():
7/16 04:26:38.197  	Format: 1920 x 1080 Window, Ds24X, multisample 8
7/16 04:26:38.295  Caps:
7/16 04:26:38.295  	numTmus: 8
7/16 04:26:38.295  	generateMipMaps: 1
7/16 04:26:38.295  	texFilterAnisotropic: 1, 16
7/16 04:26:38.295  	rttFormat: 1, 1
7/16 04:26:38.295  	pixelShaderTarget: ps_3_0
7/16 04:26:38.295  	vertexShaderTarget: vs_3_0
7/16 04:26:38.295  	vertexShaderConstants: 256
7/16 04:26:38.295  	numStreams: 16
7/16 04:26:38.295  	stereoAvailable: 0
7/16 04:26:38.295  	NVAPI: 1
7/16 04:26:38.295  	stereoHandle: 0
