7/16 05:03:55.949  CGxDevice::DeviceAdapterID(): RET: 1, VID: 10de, DID: 1c82, DVER: 0.0
7/16 05:03:56.085  CGxDevice::DeviceAdapterInfer(): RET: 1, DID: 3
7/16 05:03:56.085  ConsoleDetectDetectHardware():
7/16 05:03:56.085  	cpuIdx: 0
7/16 05:03:56.085  	videoID: 693
7/16 05:03:56.085  	soundIdx: 0
7/16 05:03:56.085  	memIdx: 0
7/16 05:03:56.126  ValidateFormatMonitor(): unable to find monitor refresh
7/16 05:03:56.126  ValidateFormatMonitor(): invalid refresh rate 60, set to 60
7/16 05:03:56.126  ConsoleDeviceInitialize(): hwDetect = 0, hwChanged = 0
7/16 05:03:56.238  CGxDeviceD3d::DeviceSetFormat():
7/16 05:03:56.238  	Format: 1920 x 1080 Window, Ds24X, multisample 8
7/16 05:03:56.359  Caps:
7/16 05:03:56.359  	numTmus: 8
7/16 05:03:56.359  	generateMipMaps: 1
7/16 05:03:56.359  	texFilterAnisotropic: 1, 16
7/16 05:03:56.359  	rttFormat: 1, 1
7/16 05:03:56.359  	pixelShaderTarget: ps_3_0
7/16 05:03:56.359  	vertexShaderTarget: vs_3_0
7/16 05:03:56.359  	vertexShaderConstants: 256
7/16 05:03:56.359  	numStreams: 16
7/16 05:03:56.359  	stereoAvailable: 0
7/16 05:03:56.359  	NVAPI: 1
7/16 05:03:56.359  	stereoHandle: 0
