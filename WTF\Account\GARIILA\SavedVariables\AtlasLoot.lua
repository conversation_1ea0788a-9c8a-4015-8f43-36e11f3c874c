
AtlasLootOptions = nil
AtlasLootDB = {
	["profileKeys"] = {
		["Anelny - Icecrown"] = "Anelny - Icecrown",
		["Dsfsdfsdfsd - Icecrown"] = "Dsfsdfsdfsd - Icecrown",
	},
	["profiles"] = {
		["Anelny - Icecrown"] = {
			["AtlasType"] = "Unknown",
			["ItemIDs"] = 1,
			["ItemSpam"] = true,
			["AllLinks"] = false,
			["AtlasLootVersion"] = "51104",
			["AtlasNaggedVersion"] = "1.16.1",
		},
		["Dsfsdfsdfsd - Icecrown"] = {
			["AtlasType"] = "Unknown",
		},
	},
}
AtlasLootWishList = {
	["Options"] = {
		["Anelny"] = {
			["AllowShareWishlistInCombat"] = true,
			["UseDefaultWishlist"] = false,
			["Mark"] = true,
			["markInTable"] = "own",
			["AllowShareWishlist"] = true,
		},
		["Dsfsdfsdfsd"] = {
			["AllowShareWishlistInCombat"] = true,
			["UseDefaultWishlist"] = false,
			["Mark"] = true,
			["AllowShareWishlist"] = true,
			["markInTable"] = "own",
		},
	},
	["Shared"] = {
	},
	["Own"] = {
		["Anelny"] = {
		},
		["Dsfsdfsdfsd"] = {
		},
	},
}
