7/16 04:34:39.203  Loading add-on !KRT
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\!KRT\!KRT.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\!KRT\KRT.xml
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  KRTSpammerName: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTSpammerName: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  KRTSpammerDuration: FontString element overriding font Chat<PERSON>ontNormal
7/16 04:34:39.203  KRTSpammerDuration: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  KRTSpammerDuration: FontString element overriding font GameFontHighlightSmall
7/16 04:34:39.203  KRTSpammerTank: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTSpammerTank: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  KRTSpammerTank: FontString element overriding font GameFontHighlightSmall
7/16 04:34:39.203  KRTSpammerTankClass: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTSpammerTankClass: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  KRTSpammerHealer: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTSpammerHealer: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  KRTSpammerHealer: FontString element overriding font GameFontHighlightSmall
7/16 04:34:39.203  KRTSpammerHealerClass: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTSpammerHealerClass: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  KRTSpammerMelee: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTSpammerMelee: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  KRTSpammerMelee: FontString element overriding font GameFontHighlightSmall
7/16 04:34:39.203  KRTSpammerMeleeClass: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTSpammerMeleeClass: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  KRTSpammerRanged: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTSpammerRanged: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  KRTSpammerRanged: FontString element overriding font GameFontHighlightSmall
7/16 04:34:39.203  KRTSpammerRangedClass: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTSpammerRangedClass: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  KRTSpammerMessage: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTSpammerMessage: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  KRTWarningsName: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTWarningsName: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  KRTWarningsContent: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  KRTMasterItemCount: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTMasterItemCount: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  KRTChangesName: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTChangesName: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  KRTChangesSpec: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTChangesSpec: FontString element overriding font GameFontHighlight
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  KRTLoggerBossBoxName: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTLoggerBossBoxDifficulty: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  KRTLoggerBossBoxTime: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  KRTLoggerPlayerBoxName: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Couldn't find inherited node: UIPanelButtonPushedTexture
7/16 04:34:39.203  Loading add-on Atlas
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\Atlas\Atlas.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\Atlas\Atlas.xml
7/16 04:34:39.203  AtlasSearchEditBox: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  Loading add-on AtlasLoot
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\AtlasLoot\AtlasLoot.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\AtlasLoot\Core\AtlasLoot.xml
7/16 04:34:39.203  AtlasLootSearchBox: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  ++ Loading file Interface\AddOns\AtlasLoot\DefaultFrame\AtlaslootDefaultFrame.xml
7/16 04:34:39.203  AtlasLootDefaultFrameSearchBox: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  Loading add-on AtlasQuest
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\AtlasQuest\AtlasQuest.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\AtlasQuest\AtlasQuest.xml
7/16 04:34:39.203  Invalid anchor point in frame: (null)
7/16 04:34:39.203  Invalid anchor point in frame: (null)
7/16 04:34:39.203  Invalid anchor point in frame: (null)
7/16 04:34:39.203  Invalid anchor point in frame: (null)
7/16 04:34:39.203  Invalid anchor point in frame: (null)
7/16 04:34:39.203  Invalid anchor point in frame: (null)
7/16 04:34:39.203  Invalid anchor point in frame: (null)
7/16 04:34:39.203  Invalid anchor point in frame: (null)
7/16 04:34:39.203  Couldn't find frame parent: AlphaMapAlphaMapFrame
7/16 04:34:39.203  Couldn't find relative frame: AlphaMapAlphaMapFrame
7/16 04:34:39.203  Loading add-on Auctionator
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\Auctionator\Auctionator.toc
7/16 04:34:39.203  Error loading Interface\AddOns\Auctionator\Locales\svSE.lua
7/16 04:34:39.203  ++ Loading file Interface\AddOns\Auctionator\Auctionator.xml
7/16 04:34:39.203  Atr_Buy_Confirm_Numstacks: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  Atr_AS_Minlevel: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  Atr_AS_Maxlevel: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  ++ Loading file Interface\AddOns\Auctionator\AuctionatorConfig.xml
7/16 04:34:39.203  Atr_Starting_Discount: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  Atr_Mem_EB_stackSize: FontString element overriding font ChatFontNormal
7/16 04:34:39.203  Loading add-on Binder
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\Binder\Binder.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\Binder\Binder.xml
7/16 04:34:39.203  Unknown child node in Backdrop element: Anchors
7/16 04:34:39.203  Loading add-on Chatter
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\Chatter\Chatter.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\Chatter\modules.xml
7/16 04:34:39.203  Error loading Interface\AddOns\Chatter\Modules\AltNames.lua
7/16 04:34:39.203  Loading add-on GearScore
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\GearScore\GearScore.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\GearScore\Frame.xml
7/16 04:34:39.203  Frame GS_DatabaseFrame: Unknown script element OnEscapePressed
7/16 04:34:39.203  Error loading Interface\AddOns\GearScore\recount.lua
7/16 04:34:39.203  Loading add-on LootLog
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\LootLog\LootLog.toc
7/16 04:34:39.203  Couldn't open Interface\AddOns\LootLog\LibDBIcon-1.0
7/16 04:34:39.203  Loading add-on MaxCamEnhanced
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\MaxCamEnhanced\MaxCamEnhanced.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\MaxCamEnhanced\Libs\Ace3\AceConfig-3.0\AceConfig-3.0.xml
7/16 04:34:39.203  Couldn't open Interface\AddOns\MaxCamEnhanced\Libs\Ace3\AceConfig-3.0\AceConfigCmd-3.0\AceConfigCmd-3.0.xml
7/16 04:34:39.203  Loading add-on MouseSpeedEnhanced
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\MouseSpeedEnhanced\MouseSpeedEnhanced.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\MouseSpeedEnhanced\Libs\Ace3\AceConfig-3.0\AceConfig-3.0.xml
7/16 04:34:39.203  Couldn't open Interface\AddOns\MouseSpeedEnhanced\Libs\Ace3\AceConfig-3.0\AceConfigCmd-3.0\AceConfigCmd-3.0.xml
7/16 04:34:39.203  Loading add-on MoveAnything
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\MoveAnything\MoveAnything.toc
7/16 04:34:39.203  Error loading Interface\AddOns\MoveAnything\UserVariables.lua
7/16 04:34:39.203  Loading add-on QDKP_V2
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\QDKP_V2\QDKP_V2.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\QDKP_V2\QDKP_V2.xml
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  Frame QDKP2_InputBox: Unknown script element OnEnterPressed
7/16 04:34:39.203  Frame QDKP2_InputBox: Unknown script element OnEscapePressed
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  Frame QDKP2_NotifyBox: Unknown script element OnEnterPressed
7/16 04:34:39.203  Frame QDKP2_NotifyBox: Unknown script element OnEscapePressed
7/16 04:34:39.203  Frame QDKP2_NotifyBox: Unknown script element OnEnterPressed
7/16 04:34:39.203  Frame QDKP2_NotifyBox: Unknown script element OnEscapePressed
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  Frame QDKP2_QuestionBox: Unknown script element OnEnterPressed
7/16 04:34:39.203  Frame QDKP2_QuestionBox: Unknown script element OnEscapePressed
7/16 04:34:39.203  Frame QDKP2_QuestionBox: Unknown script element OnEnterPressed
7/16 04:34:39.203  Frame QDKP2_QuestionBox: Unknown script element OnEscapePressed
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  Frame QDKP2_CopyWindow: Unknown script element OnEnterPressed
7/16 04:34:39.203  Frame QDKP2_CopyWindow: Unknown script element OnEscapePressed
7/16 04:34:39.203  Frame QDKP2_CopyWindow: Unknown script element OnEscapePressed
7/16 04:34:39.203  Binding header QDKP2 is defined more than once in Interface\AddOns\QDKP_V2\Bindings.xml
7/16 04:34:39.203  Binding header QDKP2 is defined more than once in Interface\AddOns\QDKP_V2\Bindings.xml
7/16 04:34:39.203  Binding header QDKP2 is defined more than once in Interface\AddOns\QDKP_V2\Bindings.xml
7/16 04:34:39.203  Binding header QDKP2 is defined more than once in Interface\AddOns\QDKP_V2\Bindings.xml
7/16 04:34:39.203  Binding header QDKP2 is defined more than once in Interface\AddOns\QDKP_V2\Bindings.xml
7/16 04:34:39.203  Binding header QDKP2 is defined more than once in Interface\AddOns\QDKP_V2\Bindings.xml
7/16 04:34:39.203  Loading add-on QDKP2_GUI
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\QDKP2_GUI\QDKP2_GUI.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\QDKP2_GUI\QDKP2_GUI.xml
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  Frame QDKP2_Frame4: Unknown script element OnEscapePressed
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  Couldn't find relative frame: QDKP2frame5_title_name
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  SETALLPOINTS set to true in frame with anchors (ignored)
7/16 04:34:39.203  Frame QDKP2_modify_log_entry: Unknown script element OnEnterPressed
7/16 04:34:39.203  Frame QDKP2_modify_log_entry: Unknown script element OnEscapePressed
7/16 04:34:39.203  Frame QDKP2_modify_log_entry: Unknown script element OnEscapePressed
7/16 04:34:39.203  Loading add-on SilverDragon
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\SilverDragon\SilverDragon.toc
7/16 04:34:39.203  Couldn't open Interface\AddOns\SilverDragon\Data\module.xml
7/16 04:34:39.203  Loading add-on TargetHealthPercent
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\TargetHealthPercent\TargetHealthPercent.toc
7/16 04:34:39.203  Couldn't open Interface\AddOns\TargetHealthPercent\Libs\Ace3\AceAddon-3.0\AceAddon-3.0.xml
7/16 04:34:39.203  Couldn't open Interface\AddOns\TargetHealthPercent\Libs\Ace3\AceEvent-3.0\AceEvent-3.0.xml
7/16 04:34:39.203  Couldn't open Interface\AddOns\TargetHealthPercent\Libs\Ace3\AceConsole-3.0\AceConsole-3.0.xml
7/16 04:34:39.203  ++ Loading file Interface\AddOns\TargetHealthPercent\Libs\Ace3\AceConfig-3.0\AceConfig-3.0.xml
7/16 04:34:39.203  Couldn't open Interface\AddOns\TargetHealthPercent\Libs\Ace3\AceConfig-3.0\AceConfigCmd-3.0\AceConfigCmd-3.0.xml
7/16 04:34:39.203  Loading add-on TellMeWhen
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\TellMeWhen\TellMeWhen.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\TellMeWhen\TellMeWhen.xml
7/16 04:34:39.203  Couldn't find relative frame: $parent
7/16 04:34:39.203  Loading add-on TidyPlates
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\TidyPlates\TidyPlates.toc
7/16 04:34:39.203  ++ Loading file Interface\AddOns\TidyPlates\TidyPlates.xml
7/16 04:34:39.203  ++ Loading file Interface\AddOns\TidyPlates\TidyPlatesHub.xml
7/16 04:34:39.203  Error loading Interface\AddOns\TidyPlates\hub\DamagePanel.lua
7/16 04:34:39.203  Error loading Interface\AddOns\TidyPlates\hub\TankPanel.lua
7/16 04:34:39.203  Loading add-on TradeSkillMaster_Auctioning
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\TradeSkillMaster_Auctioning\TradeSkillMaster_Auctioning.toc
7/16 04:34:39.203  Error loading Interface\AddOns\TradeSkillMaster_Auctioning\Locale\deDE.lua
7/16 04:34:39.203  Error loading Interface\AddOns\TradeSkillMaster_Auctioning\Locale\esES.lua
7/16 04:34:39.203  Error loading Interface\AddOns\TradeSkillMaster_Auctioning\Locale\esMX.lua
7/16 04:34:39.203  Error loading Interface\AddOns\TradeSkillMaster_Auctioning\Locale\frFR.lua
7/16 04:34:39.203  Error loading Interface\AddOns\TradeSkillMaster_Auctioning\Locale\koKR.lua
7/16 04:34:39.203  Error loading Interface\AddOns\TradeSkillMaster_Auctioning\Locale\ruRU.lua
7/16 04:34:39.203  Error loading Interface\AddOns\TradeSkillMaster_Auctioning\Locale\zhCN.lua
7/16 04:34:39.203  Error loading Interface\AddOns\TradeSkillMaster_Auctioning\Locale\zhTW.lua
7/16 04:34:39.203  Error loading Interface\AddOns\TradeSkillMaster_Auctioning\Locale\ptBR.lua
7/16 04:34:39.203  Loading add-on TradeSkillMaster_Shopping
7/16 04:34:39.203  ** Loading table of contents Interface\AddOns\TradeSkillMaster_Shopping\TradeSkillMaster_Shopping.toc
7/16 04:34:39.203  Error loading Interface\AddOns\TradeSkillMaster_Shopping\sidebar\Sniper.lua
7/16 04:34:39.203  Error loading Interface\AddOns\TradeSkillMaster_Shopping\sidebar\Crafting.lua
