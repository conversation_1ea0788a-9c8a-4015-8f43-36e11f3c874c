
ThreatPlatesDB = {
	["namespaces"] = {
		["LibDualSpec-1.0"] = {
		},
	},
	["char"] = {
		["Anelny - Icecrown"] = {
			["welcome"] = true,
			["spec"] = {
				["primary"] = false,
			},
			["specInfo"] = {
				{
					11, -- [1]
					3, -- [2]
					57, -- [3]
				}, -- [1]
				{
					20, -- [1]
					51, -- [2]
				}, -- [2]
			},
			["specName"] = {
				"Ликвидация", -- [1]
				"Бой", -- [2]
				"Скрытность", -- [3]
			},
		},
	},
	["profileKeys"] = {
		["Anelny - Icecrown"] = "Anelny - Icecrown",
	},
	["profiles"] = {
		["Icecrown"] = {
			["uniqueSettings"] = {
				[32] = {
				},
				[49] = {
				},
				[40] = {
				},
				[31] = {
				},
				[33] = {
				},
				[47] = {
				},
				["list"] = {
				},
				[39] = {
				},
				[41] = {
				},
				[48] = {
				},
				[37] = {
				},
			},
		},
		["Anelny - Icecrown"] = {
			["nameplate"] = {
				["toggle"] = {
					["Totem"] = true,
				},
			},
			["uniqueSettings"] = {
				nil, -- [1]
				nil, -- [2]
				nil, -- [3]
				nil, -- [4]
				nil, -- [5]
				nil, -- [6]
				nil, -- [7]
				nil, -- [8]
				nil, -- [9]
				nil, -- [10]
				nil, -- [11]
				nil, -- [12]
				nil, -- [13]
				nil, -- [14]
				nil, -- [15]
				nil, -- [16]
				nil, -- [17]
				nil, -- [18]
				nil, -- [19]
				nil, -- [20]
				nil, -- [21]
				nil, -- [22]
				nil, -- [23]
				nil, -- [24]
				nil, -- [25]
				nil, -- [26]
				nil, -- [27]
				nil, -- [28]
				nil, -- [29]
				nil, -- [30]
				{
				}, -- [31]
				{
				}, -- [32]
				{
				}, -- [33]
				nil, -- [34]
				nil, -- [35]
				nil, -- [36]
				{
				}, -- [37]
				nil, -- [38]
				{
				}, -- [39]
				{
				}, -- [40]
				{
				}, -- [41]
				nil, -- [42]
				nil, -- [43]
				nil, -- [44]
				nil, -- [45]
				nil, -- [46]
				{
				}, -- [47]
				{
				}, -- [48]
				{
				}, -- [49]
				["list"] = {
				},
			},
			["cache"] = {
			},
		},
		["Default"] = {
			["uniqueSettings"] = {
				[32] = {
				},
				[49] = {
				},
				[40] = {
				},
				[31] = {
				},
				[33] = {
				},
				[47] = {
				},
				["list"] = {
				},
				[39] = {
				},
				[41] = {
				},
				[48] = {
				},
				[37] = {
				},
			},
		},
		["ROGUE"] = {
			["uniqueSettings"] = {
				[32] = {
				},
				[49] = {
				},
				[40] = {
				},
				[31] = {
				},
				[33] = {
				},
				[47] = {
				},
				["list"] = {
				},
				[39] = {
				},
				[41] = {
				},
				[48] = {
				},
				[37] = {
				},
			},
		},
	},
}
