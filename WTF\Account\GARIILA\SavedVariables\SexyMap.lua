
SexyMapDB = {
	["namespaces"] = {
		["Ping"] = {
		},
		["Coordinates"] = {
			["profiles"] = {
				["My"] = {
					["y"] = 71.31695317614617,
					["x"] = -41.48084141744607,
					["fontColor"] = {
						["a"] = 1,
						["b"] = 1,
						["g"] = 1,
						["r"] = 1,
					},
					["borderColor"] = {
						["a"] = 0,
						["b"] = 0,
						["g"] = 0,
						["r"] = 0,
					},
					["locked"] = true,
					["backgroundColor"] = {
						["a"] = 1,
						["b"] = 0,
						["g"] = 0,
						["r"] = 0,
					},
					["fontSize"] = 11,
				},
				["Anelny - Icecrown"] = {
					["y"] = 71.31695317614617,
					["x"] = -41.48084141744607,
					["fontColor"] = {
						["a"] = 1,
						["r"] = 1,
						["g"] = 1,
						["b"] = 1,
					},
					["borderColor"] = {
						["a"] = 0,
						["r"] = 0,
						["g"] = 0,
						["b"] = 0,
					},
					["locked"] = true,
					["backgroundColor"] = {
						["a"] = 1,
						["r"] = 0,
						["g"] = 0,
						["b"] = 0,
					},
					["fontSize"] = 11,
				},
			},
		},
		["Buttons"] = {
			["profiles"] = {
				["My"] = {
					["direction"] = {
						["hide"] = "always",
					},
					["close"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_Omen"] = {
						["hide"] = "always",
					},
					["FuBarPluginAtlasLootFuFrameMinimapButton"] = {
						["hide"] = "always",
					},
					["AtlasButtonFrame"] = {
						["hide"] = "always",
					},
					["MiniMapLFGFrame"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_SavedInstances"] = {
						["hide"] = "always",
					},
					["lockDragging"] = false,
					["LibDBIcon10_LootLog"] = {
						["hide"] = "always",
					},
					["FuBarPluginElephantFrameMinimapButton"] = {
						["hide"] = "always",
					},
					["worldmap"] = {
						["hide"] = "never",
					},
					["DominosMinimapButton"] = {
						["hide"] = "always",
					},
					["QDKP2GUI_MiniBtn"] = {
						["hide"] = "always",
					},
					["dragPositions"] = {
						["LibDBIcon10_Omen"] = 27.07801309785591,
						["LibDBIcon10_Skada"] = -61.6511575428729,
						["LibDBIcon10_SilverDragon"] = -6.254211651639145,
						["FuBarPluginAtlasLootFuFrameMinimapButton"] = 196.1227556583228,
						["LibDBIcon10_DBM"] = -60.41206665649691,
						["MiniMapBattlefieldFrame"] = -31.32077003738145,
						["MiniMapLFGFrame"] = -20.99988315900475,
						["LibDBIcon10_SavedInstances"] = 246.617496161443,
						["MiniMapTrackingButton"] = 167.2523135127561,
						["MiniMapMailFrame"] = 141.4946933829158,
						["LibDBIcon10_LootLog"] = -45.4645737896171,
						["FuBarPluginElephantFrameMinimapButton"] = 210.*************,
						["GameTimeFrame"] = 228.*************,
						["TimeManagerClockButton"] = 267.*************,
						["MiniMapWorldMapButton"] = 142.*************,
						["KRT_MINIMAP_GUI"] = 42.**************,
						["MinimapZoomIn"] = 247.*************,
						["DominosMinimapButton"] = 10.**************,
						["DBMMinimapButton"] = 43.**************,
						["QDKP2GUI_MiniBtn"] = -5.***************,
						["LibDBIcon10_Questie"] = 152.*************,
						["LibDBIcon10_BankStack"] = -74.*************,
						["Gatherer_MinimapOptionsButton"] = 119.*************,
					},
					["Gatherer_MinimapOptionsButton"] = {
						["hide"] = "always",
					},
					["MiniMapRecordingButton"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_Skada"] = {
						["hide"] = "never",
					},
					["voice"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_DBM"] = {
						["hide"] = "always",
					},
					["MiniMapInstanceDifficulty"] = {
						["hide"] = "always",
					},
					["zoom"] = {
						["hide"] = "never",
					},
					["pvp"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_BankStack"] = {
						["hide"] = "always",
					},
					["calendar"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_SilverDragon"] = {
						["hide"] = "never",
					},
					["difficulty"] = {
						["hide"] = "always",
					},
					["mapclock"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_Questie"] = {
						["hide"] = "always",
					},
					["DBMMinimapButton"] = {
						["hide"] = "always",
					},
					["mail"] = {
						["hide"] = "always",
					},
					["KRT_MINIMAP_GUI"] = {
						["hide"] = "always",
					},
					["tracking"] = {
						["hide"] = "always",
					},
					["lfg"] = {
						["hide"] = "always",
					},
				},
				["Anelny - Icecrown"] = {
					["Gatherer_MinimapOptionsButton"] = {
						["hide"] = "always",
					},
					["direction"] = {
						["hide"] = "always",
					},
					["MiniMapRecordingButton"] = {
						["hide"] = "always",
					},
					["close"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_Omen"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_Skada"] = {
						["hide"] = "never",
					},
					["voice"] = {
						["hide"] = "always",
					},
					["FuBarPluginAtlasLootFuFrameMinimapButton"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_Questie"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_DBM"] = {
						["hide"] = "always",
					},
					["AtlasButtonFrame"] = {
						["hide"] = "always",
					},
					["mail"] = {
						["hide"] = "always",
					},
					["DBMMinimapButton"] = {
						["hide"] = "always",
					},
					["MiniMapLFGFrame"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_SavedInstances"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_LootLog"] = {
						["hide"] = "always",
					},
					["MiniMapInstanceDifficulty"] = {
						["hide"] = "always",
					},
					["zoom"] = {
						["hide"] = "never",
					},
					["lockDragging"] = false,
					["lfg"] = {
						["hide"] = "always",
					},
					["FuBarPluginElephantFrameMinimapButton"] = {
						["hide"] = "always",
					},
					["LibDBIcon10_SilverDragon"] = {
						["hide"] = "never",
					},
					["worldmap"] = {
						["hide"] = "never",
					},
					["KRT_MINIMAP_GUI"] = {
						["hide"] = "always",
					},
					["difficulty"] = {
						["hide"] = "always",
					},
					["mapclock"] = {
						["hide"] = "always",
					},
					["calendar"] = {
						["hide"] = "always",
					},
					["DominosMinimapButton"] = {
						["hide"] = "always",
					},
					["QDKP2GUI_MiniBtn"] = {
						["hide"] = "always",
					},
					["dragPositions"] = {
						["LibDBIcon10_Omen"] = 27.07801309785591,
						["LibDBIcon10_Skada"] = -61.6511575428729,
						["LibDBIcon10_SilverDragon"] = -6.254211651639145,
						["FuBarPluginAtlasLootFuFrameMinimapButton"] = 196.1227556583228,
						["LibDBIcon10_DBM"] = -61.882330699957,
						["MiniMapBattlefieldFrame"] = -31.32077003738145,
						["MiniMapLFGFrame"] = -20.99988315900475,
						["LibDBIcon10_SavedInstances"] = 246.617496161443,
						["MinimapZoomIn"] = 247.*************,
						["MiniMapMailFrame"] = 141.4946933829158,
						["LibDBIcon10_LootLog"] = -45.4645737896171,
						["FuBarPluginElephantFrameMinimapButton"] = 210.*************,
						["GameTimeFrame"] = 228.*************,
						["MiniMapWorldMapButton"] = 142.*************,
						["TimeManagerClockButton"] = 267.*************,
						["DominosMinimapButton"] = 10.**************,
						["DBMMinimapButton"] = 43.**************,
						["QDKP2GUI_MiniBtn"] = -5.***************,
						["LibDBIcon10_Questie"] = 152.*************,
						["MiniMapTrackingButton"] = 167.2523135127561,
						["Gatherer_MinimapOptionsButton"] = 119.*************,
					},
					["tracking"] = {
						["hide"] = "always",
					},
					["pvp"] = {
						["hide"] = "always",
					},
				},
			},
		},
		["AutoZoom"] = {
			["profiles"] = {
				["My"] = {
					["autoZoom"] = 0,
				},
				["Anelny - Icecrown"] = {
					["autoZoom"] = 0,
				},
			},
		},
		["Shapes"] = {
			["profiles"] = {
				["My"] = {
					["shape"] = "Interface\\BUTTONS\\WHITE8X8",
				},
				["Anelny - Icecrown"] = {
					["shape"] = "Interface\\BUTTONS\\WHITE8X8",
				},
				["Dsfsdfsdfsd - Icecrown"] = {
					["shape"] = "Textures\\MinimapMask",
				},
			},
		},
		["Fader"] = {
		},
		["Movers"] = {
			["profiles"] = {
				["My"] = {
					["scale"] = 1.2,
					["framePositions"] = {
						["DurabilityFrame"] = {
							["y"] = 645.8464108769631,
							["x"] = 1259.50910846223,
						},
						["VehicleSeatIndicator"] = {
							["y"] = 646.2789771265884,
							["x"] = 1191.510282260808,
						},
						["WatchFrame"] = {
							["y"] = 647.4271649570654,
							["x"] = 1012.708179134356,
						},
					},
				},
				["Anelny - Icecrown"] = {
					["scale"] = 1.2,
					["framePositions"] = {
						["DurabilityFrame"] = {
							["y"] = 645.8464108769631,
							["x"] = 1259.50910846223,
						},
						["VehicleSeatIndicator"] = {
							["y"] = 646.2789771265884,
							["x"] = 1191.510282260808,
						},
						["WatchFrame"] = {
							["y"] = 647.4271649570654,
							["x"] = 1012.708179134356,
						},
					},
				},
			},
		},
		["Borders"] = {
			["profiles"] = {
				["My"] = {
					["applyPreset"] = false,
					["borders"] = {
						{
							["a"] = 1,
							["hNudge"] = 0,
							["rotSpeed"] = 10,
							["r"] = 0.3411764705882353,
							["scale"] = 0.73,
							["g"] = 0.4705882352941176,
							["vNudge"] = 0,
							["disableRotation"] = true,
							["name"] = "Square Overlay",
							["blendMode"] = "ADD",
							["b"] = 1,
							["drawLayer"] = "ARTWORK",
							["rotation"] = 66,
							["texture"] = "World\\GENERIC\\ACTIVEDOODADS\\WORLDTREEPORTALS\\TWISTEDNETHER8.BLP",
						}, -- [1]
						{
							["a"] = 1,
							["hNudge"] = 0,
							["rotSpeed"] = -14,
							["b"] = 1,
							["scale"] = 1.9,
							["g"] = 0.7215686274509804,
							["vNudge"] = 5,
							["disableRotation"] = true,
							["name"] = "Circle 2",
							["drawLayer"] = "BACKGROUND",
							["r"] = 0.3607843137254902,
							["blendMode"] = "ADD",
							["texture"] = "World\\GENERIC\\ACTIVEDOODADS\\INSTANCEPORTAL\\GENERICGLOW2.BLP",
						}, -- [2]
					},
				},
				["Anelny - Icecrown"] = {
					["applyPreset"] = false,
					["borders"] = {
						{
							["a"] = 1,
							["hNudge"] = 0,
							["rotSpeed"] = 10,
							["r"] = 0.3411764705882353,
							["scale"] = 0.73,
							["g"] = 0.4705882352941176,
							["vNudge"] = 0,
							["disableRotation"] = true,
							["name"] = "Square Overlay",
							["blendMode"] = "ADD",
							["rotation"] = 66,
							["drawLayer"] = "ARTWORK",
							["b"] = 1,
							["texture"] = "World\\GENERIC\\ACTIVEDOODADS\\WORLDTREEPORTALS\\TWISTEDNETHER8.BLP",
						}, -- [1]
						{
							["a"] = 1,
							["hNudge"] = 0,
							["rotSpeed"] = -14,
							["b"] = 1,
							["scale"] = 1.9,
							["g"] = 0.7215686274509804,
							["vNudge"] = 5,
							["disableRotation"] = true,
							["name"] = "Circle 2",
							["drawLayer"] = "BACKGROUND",
							["blendMode"] = "ADD",
							["r"] = 0.3607843137254902,
							["texture"] = "World\\GENERIC\\ACTIVEDOODADS\\INSTANCEPORTAL\\GENERICGLOW2.BLP",
						}, -- [2]
					},
				},
				["Dsfsdfsdfsd - Icecrown"] = {
					["applyPreset"] = false,
					["borders"] = {
						{
							["a"] = 1,
							["r"] = 0.3098039215686275,
							["name"] = "Rune 1",
							["b"] = 1,
							["scale"] = 1.4,
							["rotSpeed"] = -16,
							["g"] = 0.4784313725490196,
							["texture"] = "SPELLS\\AURARUNE256.BLP",
						}, -- [1]
						{
							["a"] = 0.3799999952316284,
							["r"] = 0.196078431372549,
							["rotSpeed"] = 4,
							["b"] = 1,
							["scale"] = 2.1,
							["name"] = "Rune 2",
							["g"] = 0.2901960784313725,
							["texture"] = "SPELLS\\AuraRune_A.blp",
						}, -- [2]
						{
							["a"] = 0.3,
							["name"] = "Fade",
							["b"] = 1,
							["scale"] = 1.6,
							["r"] = 0,
							["g"] = 0.2235294117647059,
							["texture"] = "SPELLS\\T_VFX_HERO_CIRCLE.BLP",
						}, -- [3]
					},
				},
			},
		},
		["HudMap"] = {
			["profiles"] = {
				["My"] = {
					["setNewScale"] = true,
					["scale"] = 1.4,
				},
				["Anelny - Icecrown"] = {
					["scale"] = 1.4,
					["setNewScale"] = true,
				},
				["Dsfsdfsdfsd - Icecrown"] = {
					["scale"] = 1.4,
					["setNewScale"] = true,
				},
			},
		},
		["ZoneText"] = {
		},
	},
	["profileKeys"] = {
		["Dsfsdfsdfsd - Icecrown"] = "Dsfsdfsdfsd - Icecrown",
		["Anelny - Icecrown"] = "My",
	},
	["profiles"] = {
		["My"] = {
		},
		["Anelny - Icecrown"] = {
		},
	},
}
