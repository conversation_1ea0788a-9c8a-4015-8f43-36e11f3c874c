-- LortiUI Options Interface
-- Separate file for clean organization

local LortiUI = LibStub("AceAddon-3.0"):GetAddon("LortiUI")
local L = LortiUI_Locale

-- Simple helper functions
local function createToggle(key, name, desc, order, disabled)
    return {
        type = "toggle",
        name = name,
        desc = desc,
        order = order,
        get = function(info)
            return LortiUI.db and LortiUI.db.profile and LortiUI.db.profile[key] or false
        end,
        set = function(info, value)
            if LortiUI.db and LortiUI.db.profile then
                LortiUI.db.profile[key] = value
                if LortiUI.ApplyAllColors then
                    LortiUI:ApplyAllColors()
                end
            end
        end,
        disabled = disabled,
    }
end

local function createColorOption(key, name, desc, order, hasAlpha, disabled)
    return {
        type = "color",
        name = name,
        desc = desc,
        order = order,
        hasAlpha = hasAlpha,
        get = function(info)
            if not LortiUI.db or not LortiUI.db.profile then
                return 0, 0, 0, 1
            end
            local color = LortiUI.db.profile[key] or {r = 0, g = 0, b = 0, a = 1}
            if hasAlpha then
                return color.r or 0, color.g or 0, color.b or 0, color.a or 1
            else
                return color.r or 0, color.g or 0, color.b or 0
            end
        end,
        set = function(info, r, g, b, a)
            if LortiUI.db and LortiUI.db.profile then
                if hasAlpha then
                    LortiUI.db.profile[key] = {r = r, g = g, b = b, a = a}
                else
                    LortiUI.db.profile[key] = {r = r, g = g, b = b}
                end
                if LortiUI.ApplyAllColors then
                    LortiUI:ApplyAllColors()
                end
            end
        end,
        disabled = disabled,
    }
end

-- Tab 1: Frame Colors
local function CreateFrameColorsTab()
    return {
        type = "group",
        name = L["Frame Colors"] or "Frame Colors",
        order = 1,
        args = {
            description = {
                type = "description",
                name = L["Customize the colors of unit frames (player, target, focus, etc.)"] or "Customize the colors of unit frames (player, target, focus, etc.)",
                order = 1,
            },

            -- Player Frame
            playerHeader = {
                type = "header",
                name = L["Player Frame"] or "Player Frame",
                order = 10,
            },
            enablePlayerColor = createToggle("enablePlayerColor", L["Enable Player Color"] or "Enable Player Color", L["Enable color for player frame"] or "Enable color for player frame", 11),
            playerColor = createColorOption("playerColor", L["Player Frame Color"] or "Player Frame Color", L["Sets the color for player frame texture"] or "Sets the color for player frame texture", 12),
            
            -- Target Frame
            targetHeader = {
                type = "header",
                name = L["Target Frame"] or "Target Frame",
                order = 20,
            },
            enableTargetColor = createToggle("enableTargetColor", L["Enable Target Color"] or "Enable Target Color", L["Enable color for target frame"] or "Enable color for target frame", 21),
            targetColor = createColorOption("targetColor", L["Target Frame Color"] or "Target Frame Color", L["Sets the color for target frame texture"] or "Sets the color for target frame texture", 22),

            -- Focus Frame
            focusHeader = {
                type = "header",
                name = L["Focus Frame"] or "Focus Frame",
                order = 30,
            },
            enableFocusColor = createToggle("enableFocusColor", L["Enable Focus Color"] or "Enable Focus Color", L["Enable color for focus frame"] or "Enable color for focus frame", 31),
            focusColor = createColorOption("focusColor", L["Focus Frame Color"] or "Focus Frame Color", L["Sets the color for focus frame texture"] or "Sets the color for focus frame texture", 32),

            -- Party Frames
            partyHeader = {
                type = "header",
                name = L["Party Frames"] or "Party Frames",
                order = 40,
            },
            enablePartyColor = createToggle("enablePartyColor", L["Enable Party Color"] or "Enable Party Color", L["Enable color for party frames"] or "Enable color for party frames", 41),
            partyColor = createColorOption("partyColor", L["Party Frame Color"] or "Party Frame Color", L["Sets the color for party member frame textures"] or "Sets the color for party member frame textures", 42),

            -- Pet Frame
            petHeader = {
                type = "header",
                name = L["Pet Frame"] or "Pet Frame",
                order = 50,
            },
            enablePetColor = createToggle("enablePetColor", L["Enable Pet Color"] or "Enable Pet Color", L["Enable color for pet frame"] or "Enable color for pet frame", 51),
            petColor = createColorOption("petColor", L["Pet Frame Color"] or "Pet Frame Color", L["Sets the color for pet frame texture"] or "Sets the color for pet frame texture", 52),

            -- PvP Frames
            pvpHeader = {
                type = "header",
                name = "PvP Frames",
                order = 60,
            },
            enableBossColor = createToggle("enableBossColor", L["Enable Boss Color"] or "Enable Boss Color", L["Enable color for boss frames"] or "Enable color for boss frames", 61),
            bossColor = createColorOption("bossColor", L["Boss Frame Color"] or "Boss Frame Color", L["Sets the color for boss frame textures"] or "Sets the color for boss frame textures", 62),
            enableArenaColor = createToggle("enableArenaColor", L["Enable Arena Color"] or "Enable Arena Color", L["Enable color for arena frames"] or "Enable color for arena frames", 63),
            arenaColor = createColorOption("arenaColor", L["Arena Frame Color"] or "Arena Frame Color", L["Sets the color for arena frame textures"] or "Sets the color for arena frame textures", 64),

            -- Vehicle Frame
            vehicleHeader = {
                type = "header",
                name = L["Vehicle Frame"] or "Vehicle Frame",
                order = 70,
            },
            enableVehicleColor = createToggle("enableVehicleColor", L["Enable Vehicle Color"] or "Enable Vehicle Color", L["Enable color for vehicle frame"] or "Enable color for vehicle frame", 71),
            vehicleColor = createColorOption("vehicleColor", L["Vehicle Frame Color"] or "Vehicle Frame Color", L["Sets the color for vehicle frame texture"] or "Sets the color for vehicle frame texture", 72),
        }
    }
end

-- Export the function
LortiUI.CreateFrameColorsTab = CreateFrameColorsTab
