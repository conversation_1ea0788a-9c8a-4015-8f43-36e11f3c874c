-- LortiUI Media Effects Options
-- Separate file for media effects configuration

local LortiUI = LibStub("AceAddon-3.0"):GetAddon("LortiUI")
local L = LortiUI_Locale

-- Simple helper functions
local function createToggle(key, name, desc, order, disabled)
    return {
        type = "toggle",
        name = name,
        desc = desc,
        order = order,
        get = function(info) return LortiUI.db.profile[key] end,
        set = function(info, value)
            LortiUI.db.profile[key] = value
            if LortiUI.InitializeMediaEnhancements then
                LortiUI:InitializeMediaEnhancements()
            end
        end,
        disabled = disabled,
    }
end

local function createRange(key, name, desc, order, min, max, step, disabled)
    return {
        type = "range",
        name = name,
        desc = desc,
        order = order,
        min = min,
        max = max,
        step = step,
        get = function(info) return LortiUI.db.profile[key] end,
        set = function(info, value)
            LortiUI.db.profile[key] = value
            if <PERSON>rti<PERSON>.InitializeMediaEnhancements then
                LortiUI:InitializeMediaEnhancements()
            end
        end,
        disabled = disabled,
    }
end

-- Tab 3: Media Effects
local function CreateMediaEffectsTab()
    return {
        type = "group",
        name = L["Media Effects"] or "Media Effects",
        order = 3,
        args = {
            description = {
                type = "description",
                name = L["Add visual effects and enhancements to your interface"] or "Add visual effects and enhancements to your interface",
                order = 1,
            },

            -- Glow Effects
            glowHeader = {
                type = "header",
                name = L["Glow Effects"] or "Glow Effects",
                order = 10,
            },
            enableGlowEffects = createToggle("enableGlowEffects", L["Enable Glow Effects"] or "Enable Glow Effects", L["Enable glow effects on frames"] or "Enable glow effects on frames", 11),
            enablePlayerGlow = createToggle("enablePlayerGlow", L["Enable Player Glow"] or "Enable Player Glow", L["Enable glow effect on player frame"] or "Enable glow effect on player frame", 12,
                function() return not LortiUI.db.profile.enableGlowEffects end),
            enableTargetGlow = createToggle("enableTargetGlow", L["Enable Target Glow"] or "Enable Target Glow", L["Enable glow effect on target frame"] or "Enable glow effect on target frame", 13,
                function() return not LortiUI.db.profile.enableGlowEffects end),

            -- Glow Settings
            glowSettingsHeader = {
                type = "header",
                name = L["Glow Settings"] or "Glow Settings",
                order = 20,
            },
            glowIntensity = createRange("glowIntensity", L["Glow Intensity"] or "Glow Intensity", L["Intensity of the glow effect"] or "Intensity of the glow effect", 21, 0.1, 1.0, 0.01,
                function() return not LortiUI.db.profile.enableGlowEffects end),
            glowSize = createRange("glowSize", L["Glow Size"] or "Glow Size", L["Size of the glow effect"] or "Size of the glow effect", 22, 1, 10, 1,
                function() return not LortiUI.db.profile.enableGlowEffects end),

            -- Transparency Effects
            transparencyHeader = {
                type = "header",
                name = L["Transparency Effects"] or "Transparency Effects",
                order = 30,
            },
            enableTransparencyEffects = createToggle("enableTransparencyEffects", L["Enable Transparency Effects"] or "Enable Transparency Effects", L["Enable transparency effects on frames"] or "Enable transparency effects on frames", 31),
            frameTransparency = createRange("frameTransparency", L["Frame Transparency"] or "Frame Transparency", L["Transparency level for frames"] or "Transparency level for frames", 32, 0.1, 1.0, 0.01,
                function() return not LortiUI.db.profile.enableTransparencyEffects end),

            -- Animation Effects
            animationHeader = {
                type = "header",
                name = L["Animation Effects"] or "Animation Effects",
                order = 40,
            },
            enableAnimations = createToggle("enableAnimations", L["Enable Animations"] or "Enable Animations", L["Enable animation effects"] or "Enable animation effects", 41),
            animationSpeed = createRange("animationSpeed", L["Animation Speed"] or "Animation Speed", L["Speed of animations"] or "Speed of animations", 42, 0.5, 3.0, 0.1,
                function() return not LortiUI.db.profile.enableAnimations end),

            -- Coming Soon
            comingSoonHeader = {
                type = "header",
                name = "Coming Soon",
                order = 50,
            },
            comingSoonDesc = {
                type = "description",
                name = "More media effects will be added in future updates.",
                order = 51,
            },
        }
    }
end

-- Export the function
LortiUI.CreateMediaEffectsTab = CreateMediaEffectsTab
